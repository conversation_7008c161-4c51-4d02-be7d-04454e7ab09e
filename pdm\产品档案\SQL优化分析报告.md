# SQL优化分析报告

## 🔍 原始查询问题分析

### 执行计划显示的问题
```
1,SIMPLE,pv,,ALL,,,,,50053,4,Using where; Using temporary; Using filesort
1,SIMPLE,pc,,index,,product,801,,25992,100,Using where; Using index; Using join buffer (hash join)
1,SIMPLE,pb,,ref,idx_pdm_product_bom_code_version,idx_pdm_product_bom_code_version,186,"h3chq_pdmbusiness1704287270935.pv.material_code,h3chq_pdmbusiness1704287270935.pv.product_version",4,100,Using index
1,SIMPLE,ppc,,ref,"product,idx_pdm_product_craft_code_version",idx_pdm_product_craft_code_version,1536,"h3chq_pdmbusiness1704287270935.pv.material_code,h3chq_pdmbusiness1704287270935.pv.product_version",4,100,Using where; Using index
1,SIMPLE,pf,,ref,material_code,material_code,303,h3chq_pdmbusiness1704287270935.pv.material_code,1,100,Using where
```

### 主要性能问题

1. **DISTINCT导致的问题**
   - 需要额外的排序和去重操作
   - 产生临时表：`Using temporary; Using filesort`
   - 增加内存消耗和CPU开销

2. **多个LEFT JOIN的问题**
   - 扫描行数过多：pv表50,053行，pc表25,992行
   - 产生笛卡尔积，导致中间结果集膨胀
   - 使用连接缓冲区：`Using join buffer (hash join)`

3. **条件判断逻辑问题**
   - 即使参数为空，也会执行所有JOIN操作
   - OR条件影响索引使用效率
   - 关联表条件无法有效过滤数据

## 🚀 优化方案详解

### 1. 移除DISTINCT
**问题**：DISTINCT需要对结果集进行排序和去重
**解决**：使用EXISTS替代LEFT JOIN，天然避免重复数据

### 2. 使用EXISTS替代LEFT JOIN
**原理**：EXISTS只判断存在性，不返回具体数据，避免笛卡尔积

**优化前**：
```sql
LEFT JOIN pdm_product_client pc ON pv.material_code = pc.product_code
WHERE ((:client_name is null or :client_name = '') or (pc.client_name like concat ('%',:client_name,'%')))
```

**优化后**：
```sql
AND IF(:client_name IS NULL OR :client_name = '', 1,
  EXISTS (SELECT 1 FROM pdm_product_client pc 
          WHERE pc.product_code = pv.material_code 
            AND pc.client_name LIKE CONCAT('%', :client_name, '%')))
```

### 3. 条件逻辑优化
**核心思想**：只在参数有值时才执行相关子查询

**优势**：
- 避免不必要的表扫描
- 提高索引使用效率
- 减少CPU和I/O开销

## 📊 性能对比预测

| 指标 | 原始查询 | 优化后查询 | 改善程度 |
|------|----------|------------|----------|
| 扫描行数 | 50,053 + 25,992 | < 1,000 | 95%+ 减少 |
| 临时表 | 是 | 否 | 消除 |
| 文件排序 | 是 | 否 | 消除 |
| 连接缓冲区 | 需要 | 不需要 | 消除 |
| 查询时间 | 几秒 | 几十毫秒 | 90%+ 提升 |

## 🔧 建议的索引优化

### 主表索引
```sql
-- 优化主查询条件
CREATE INDEX idx_pdm_product_version_main 
ON pdm_product_version (flag_deleted, status, id DESC);

-- 优化搜索条件
CREATE INDEX idx_pdm_product_version_search 
ON pdm_product_version (material_code, product_version, flag_deleted, status);
```

### 关联表索引
```sql
-- 客户端表
CREATE INDEX idx_pdm_product_client_search 
ON pdm_product_client (product_code, client_name);

-- BOM表
CREATE INDEX idx_pdm_product_bom_search 
ON pdm_product_bom (product_code, product_version, material_code, material_name);

-- 工艺表
CREATE INDEX idx_pdm_product_craft_search 
ON pdm_product_craft (product_code, product_version, craft_name);

-- 文件表
CREATE INDEX idx_pdm_upload_file_search 
ON pdm_upload_file (material_code, product_version, file_id);
```

## 🎯 实施建议

### 阶段1：验证优化效果
1. 在测试环境执行优化后的SQL
2. 使用EXPLAIN分析执行计划
3. 对比查询时间和资源消耗

### 阶段2：索引优化
1. 创建建议的索引
2. 更新表统计信息：`ANALYZE TABLE`
3. 再次测试性能

### 阶段3：生产环境部署
1. 选择低峰期部署
2. 监控查询性能
3. 根据实际情况微调

## 📈 监控指标

### 关键性能指标
- **查询时间**：目标 < 100ms
- **扫描行数**：目标 < 1000行
- **CPU使用率**：目标降低80%+
- **内存使用**：目标降低90%+

### 监控SQL
```sql
-- 查看慢查询日志
SHOW VARIABLES LIKE 'slow_query_log%';

-- 查看查询执行统计
SELECT * FROM performance_schema.events_statements_summary_by_digest 
WHERE DIGEST_TEXT LIKE '%pdm_product_version%'
ORDER BY AVG_TIMER_WAIT DESC;
```

## 🔄 回滚方案

如果优化后出现问题，可以快速回滚到原始查询：
1. 保留原始SQL作为备份
2. 准备回滚脚本
3. 监控业务指标，确保功能正常

## 📝 总结

这次优化主要解决了：
1. **DISTINCT导致的性能问题** - 通过EXISTS避免重复数据
2. **多表JOIN的笛卡尔积问题** - 使用条件化的EXISTS子查询
3. **不必要的表扫描问题** - 只在参数有值时执行相关查询

预期可以将查询性能提升90%以上，从几秒优化到几十毫秒级别。
