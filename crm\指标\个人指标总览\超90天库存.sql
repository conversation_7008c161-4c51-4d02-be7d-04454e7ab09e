
-- 获取今年和去年
select temp1.`year`, temp1.`month`, ifnull(temp2.cust_size, 0) cust_size
from (select `year_month`, `year`, `month` from cockpit.dim_month dm where dm.year in (year(now()), year(now()) - 1)) temp1
         left join (select update_time, count(*) cust_size
                    from cockpit.dws_crm_sale_inventory_month
                    where item_age > 90
                      AND if(:admin,
                             1,
                             if(:cust_code_size > 0, cust_code in (:cust_code_arr), 1)
                          )
                    group by update_time) temp2 on temp1.`year_month` = temp2.update_time
order by year asc,month asc
;


