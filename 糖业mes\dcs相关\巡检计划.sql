select * from dev_device_item_plan_relation;

select a.id,
       a.item_code,
       a.inspect_item,
       a.reference_value,
       a.item_type,
       a.value_msg,
       a.source,
       a.attribute,
       coalesce(a.result, '') as result,
       a.reference_curve,
       coalesce(a.notes, '') as notes,
       a.relation_code,
       b.inspect_item_content,
       b.inspect_item_method
from dev_inspect_task_record a
         left join
     dev_inspect_item b on b.inspect_item_code = a.item_code
where a.relation_code = 15445;
select * from dev_inspect_task where task_code='XJRW202505190016';
