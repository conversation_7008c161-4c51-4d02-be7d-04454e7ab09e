-- 当年新客户开发对比
select year(now()) - 1 metric_year,
       count(*)                        cust_size,
       ifnull(sum(invoiced_amount), 0) invoiced_amount_new,
       ifnull(sum( case when metric_year < year(now())-2 then  ifnull(invoiced_amount, 0) end),0) invoiced_amount_old
from cockpit.dws_new_customer dnc
where metric_year = year(now()) - 1
union all
select year(now())  metric_year,
       count(*)                        cust_size,
       ifnull(sum( case when metric_year = year(now()) then  ifnull(invoiced_amount, 0) end),0)  invoiced_amount_new,
       ifnull(sum( case when metric_year < year(now())-1 then  ifnull(invoiced_amount, 0) end),0) invoiced_amount_old
from cockpit.dws_new_customer dnc;
-- 去年
select year(now()) - 1;
