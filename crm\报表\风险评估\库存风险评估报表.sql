-- 库存金额
select cust_code,
       cust_name,
       sum(inventory_cost) inventory_cost,
       max(item_age)       item_age
from dwd_crm_sale_inventory_detail
where cust_code is not null
group by cust_code, cust_name;

-- 库存评分
select cust_code,
       cust_name,
       inventory_cost,
       inventory_age,
       risk_score,
       case
           when risk_score >= 36 then '重大风险'
           when risk_score >= 24 then '高风险'
           when risk_score >= 13 then '中等风险'
           when risk_score >= 7 then '一般'
           when risk_score >= 3 then '低风险'
           else '无风险' end                                            risk_level,
       DATE_FORMAT(DATE_SUB(CURRENT_DATE(), INTERVAL 1 MONTH), '%Y') AS data_year,
       DATE_FORMAT(DATE_SUB(CURRENT_DATE(), INTERVAL 1 MONTH), '%m') AS data_month,
       1                                                                type
from (select cust_code,
             cust_name,
             round(sum(inventory_cost), 2) inventory_cost,
             max(item_age)                 inventory_age,
             CASE
                 -- >=10万的档位
                 WHEN sum(inventory_cost) >= 100000 and max(item_age) > 150 THEN 36
                 WHEN sum(inventory_cost) >= 100000 and max(item_age) >= 121 THEN 30
                 WHEN sum(inventory_cost) >= 100000 and max(item_age) >= 91 THEN 24
                 WHEN sum(inventory_cost) >= 100000 and max(item_age) >= 61 THEN 18
                 WHEN sum(inventory_cost) >= 100000 and max(item_age) >= 30 THEN 12
                 WHEN sum(inventory_cost) >= 100000 and max(item_age) >= 15 THEN 6

                 -- 5-10万的档位
                 WHEN sum(inventory_cost) >= 50000 and max(item_age) > 150 THEN 30
                 WHEN sum(inventory_cost) >= 50000 and max(item_age) >= 121 THEN 25
                 WHEN sum(inventory_cost) >= 50000 and max(item_age) >= 91 THEN 20
                 WHEN sum(inventory_cost) >= 50000 and max(item_age) >= 61 THEN 15
                 WHEN sum(inventory_cost) >= 50000 and max(item_age) >= 30 THEN 10
                 WHEN sum(inventory_cost) >= 50000 and max(item_age) >= 15 THEN 5

                 -- 3-5万的档位
                 WHEN sum(inventory_cost) >= 30000 and max(item_age) > 150 THEN 24
                 WHEN sum(inventory_cost) >= 30000 and max(item_age) >= 121 THEN 20
                 WHEN sum(inventory_cost) >= 30000 and max(item_age) >= 91 THEN 16
                 WHEN sum(inventory_cost) >= 30000 and max(item_age) >= 61 THEN 12
                 WHEN sum(inventory_cost) >= 30000 and max(item_age) >= 30 THEN 8
                 WHEN sum(inventory_cost) >= 30000 and max(item_age) >= 15 THEN 4

                 ELSE 0
                 END as                    risk_score

      from dwd_crm_sale_inventory_detail
      where cust_code is not null
      group by cust_code, cust_name) temp
order by risk_score desc;


SELECT DATE_FORMAT(DATE_SUB(CURRENT_DATE(), INTERVAL 1 MONTH), '%Y-%c') AS last_month;
