-- 投诉与折让
SELECT '西安环球'                                   sale_company,
       t3.deparment_code,
       t3.deparment_name,
       t3.region,
       t3.cust_name,
       t3.cust_manager_code,
       t3.cust_manager_name,
       t3.cust_type,
       CASE
           WHEN t3.cust_type = 0 THEN '新建客户'
           WHEN t3.cust_type = 1 THEN '公海客户'
           WHEN t3.cust_type = 2 THEN '合作客户'
           WHEN t3.cust_type = 3 THEN '开发中客户'
           WHEN t3.cust_type = 4 THEN '受限客户'
           END                                      cust_type_name,
       t1.sales_order_code,
       t4.complaint_time,
       t2.main_class,
       t2.sub_class,
       t2.material_code,
       t2.material_name,
       1                                            complaint_count,
       round(delivery_amount , 2) delivery_amount
FROM crm_sales_order t1
         LEFT JOIN crm_sales_order_product t2 ON
    t1.sales_order_code = t2.sales_order_code
         LEFT JOIN crm_cust_basic t3 ON
    t3.cust_code = t1.cust_code
         left join crm_customer_complaint t4 on t4.sales_order_code = t1.sales_order_code
WHERE t1.flag_deleted = 0
  AND t2.flag_deleted = 0
  AND t3.flag_deleted = 0
  AND t4.flag_deleted = 0
  AND if(:admin,
         1,
         if(:cust_code_size > 0, t3.cust_code in (:cust_code_arr), 1)
      )
  AND ((:deparment_code IS NULL OR :deparment_code = '') OR (t3.deparment_code = :deparment_code))
  AND ((:region IS NULL OR :region = '') OR (department_region = :region))
  AND ((:cust_code IS NULL OR :cust_code = '') OR (t3.cust_code like concat('%', :cust_code, '%')))
  AND ((:cust_name IS NULL OR :cust_name = '') OR (t3.cust_name LIKE concat('%', :cust_name, '%')))
  AND ((:material_code IS NULL OR :material_code = '') OR (t2.material_code like concat('%', :material_code, '%')))
  AND ((:material_name IS NULL OR :material_name = '') OR (t2.material_name LIKE concat('%', :material_name, '%')))
  AND ((:complaint_start_time IS NULL OR :complaint_start_time = '') OR
       (t4.complaint_time >= :complaint_start_time))
  AND ((:complaint_end_time IS NULL OR :complaint_end_time = '') OR
       (t4.complaint_time <= :complaint_end_time))
ORDER BY t1.id ASC
LIMIT :page_size offset :offset;

-- 合计
SELECT '合计'                                            sale_company,
       count(*)                                          complaint_count,
       round(sum(delivery_amount ), 2) delivery_amount
FROM crm_sales_order t1
         LEFT JOIN crm_sales_order_product t2 ON
    t1.sales_order_code = t2.sales_order_code
         LEFT JOIN crm_cust_basic t3 ON
    t3.cust_code = t1.cust_code
         left join crm_customer_complaint t4 on t4.sales_order_code = t1.sales_order_code
WHERE t1.flag_deleted = 0
  AND t2.flag_deleted = 0
  AND t3.flag_deleted = 0
  AND t4.flag_deleted = 0
  AND if(:admin,
         1,
         if(:cust_code_size > 0, t3.cust_code in (:cust_code_arr), 1)
      )
  AND ((:deparment_code IS NULL OR :deparment_code = '') OR (t3.deparment_code = :deparment_code))
  AND ((:region IS NULL OR :region = '') OR (department_region = :region))
  AND ((:cust_code IS NULL OR :cust_code = '') OR (t3.cust_code like concat('%', :cust_code, '%')))
  AND ((:cust_name IS NULL OR :cust_name = '') OR (t3.cust_name LIKE concat('%', :cust_name, '%')))
  AND ((:material_code IS NULL OR :material_code = '') OR (t2.material_code like concat('%', :material_code, '%')))
  AND ((:material_name IS NULL OR :material_name = '') OR (t2.material_name LIKE concat('%', :material_name, '%')))
  AND ((:complaint_start_time IS NULL OR :complaint_start_time = '') OR
       (t4.complaint_time >= :complaint_start_time))
  AND ((:complaint_end_time IS NULL OR :complaint_end_time = '') OR
       (t4.complaint_time <= :complaint_end_time))
;
