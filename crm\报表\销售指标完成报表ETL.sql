-- 销售指标完成统计
SELECT '西安环球'                                            sale_company,
       t3.deparment_code,
       t3.deparment_name,
       t3.department_region                                  region,
       t3.cust_code,
       t3.cust_name,
       t4.sales_code                                         cust_manager_code,
       t4.sales_name                                         cust_manager_name,
       t3.sales_assistant_code,
       t3.sales_assistant_name,
       t3.cust_type,
       CASE
           WHEN t3.cust_type = 0 THEN '新建客户'
           WHEN t3.cust_type = 1 THEN '公海客户'
           WHEN t3.cust_type = 2 THEN '合作客户'
           WHEN t3.cust_type = 3 THEN '开发中客户'
           WHEN t3.cust_type = 4 THEN '受限客户'
           END                                               cust_type_name,
       t1.sales_order_code,
       STR_TO_DATE(t1.create_time, '%Y-%m-%d')               create_time,
       t1.factory_assigned,
       t1.status,
       case
           when t1.status = 10 then '已删除'
           when t1.status = 8 then '已取消'
           when t1.status = 7 then '已关闭'
           when t1.status = 6 then '已开票'
           when (select count(*)
                 from invoice_application ia
                 where ia.status IN ('0', '3', '4')
                   and ia.flag_deleted = 0
                   and ia.split_order_no like concat('%', t1.sales_order_code, '%')) > 0
               then '已发货未开票'
           when t1.status = 0 then '已拆分'
           when t1.status = 1 then '已下达'
           when t1.status = 2 then '已排产'
           when t1.status = 3 then '已领料'
           when t1.status = 4 then '生产中'
           when t1.status = 5 then '已入库'
           else '其他'
           end                                               status_desc,
       t2.main_class,
       t2.sub_class,
       t2.material_code,
       t2.material_name,
       t2.order_quantity_after_split                         product_quantity,
       round(IF(t2.unit_price_exclusive NOT REGEXP '^-?[0-9]+(\\.[0-9]+)?$', 0,
                t2.unit_price_exclusive),
             6)                                              unit_price_exclusive,
       round(IF(t2.amount_exclusive_tax NOT REGEXP '^-?[0-9]+(\\.[0-9]+)?$', 0,
                t2.amount_exclusive_tax),
             2)                                              amount_exclusive_tax,
       ifnull(t5.invoiced_quantity, 0)                       invoiced_quantity,
       ifnull(t5.invoiced_amount, 0)*t4.target_percentage/100        invoiced_amount,
       t6.sales_target * 10000                               sales_target,
       round(100 * ifnull(t5.invoiced_amount, 0) / (ifnull(t6.sales_target, 0) * 10000),4) completion_ratio,
       t2.contract_product_line_number,
       t5.invoiced_date
FROM crm_sales_order t1
         LEFT JOIN crm_sales_order_product t2 ON t1.sales_order_code = t2.sales_order_code
         LEFT JOIN crm_cust_basic t3 ON t3.cust_code = t1.cust_code
         left join (WITH deduplicated_sales AS (SELECT DISTINCT ccb.cust_code,
                                                                ccb.cust_manager_code,
                                                                ccb.cust_manager_name,
                                                                ifnull(ccs.sales_code, ccb.cust_manager_code) sales_code,
                                                                ifnull(ccs.sales_name, ccb.cust_manager_name) sales_name,
                                                                NULLIF(
                                                                        CAST(REPLACE(REPLACE(ccs.target_percentage, '%', ''), ' ', '') AS DECIMAL),
                                                                        0) as                                 cleaned_percentage
                                                FROM crm_cust_basic ccb
                                                         LEFT JOIN crm_cust_sale ccs
                                                                   ON ccb.cust_mnemonic_code = ccs.cust_mnemonic_code
                                                                       AND ccb.cust_version = ccs.cust_version
                                                                       AND ccs.flag_deleted = 0
                                                WHERE cust_status = 2
                                                  AND cust_type NOT IN (0, 1)
                                                  AND ccb.flag_deleted = 0)
                    SELECT cust_code,
                           sales_code,
                           sales_name,
                           COALESCE(
                                   cleaned_percentage,
                                   CASE
                                       WHEN SUM(CASE WHEN cleaned_percentage IS NULL THEN 1 ELSE 0 END)
                                                OVER (PARTITION BY cust_code)
                                           = COUNT(*) OVER (PARTITION BY cust_code)
                                           THEN 100.0 / COUNT(*) OVER (PARTITION BY cust_code)
                                       ELSE (100 - SUM(COALESCE(cleaned_percentage, 0)) OVER (PARTITION BY cust_code)) /
                                            NULLIF(SUM(CASE WHEN cleaned_percentage IS NULL THEN 1 ELSE 0 END)
                                                       OVER (PARTITION BY cust_code), 0)
                                       END,
                                   0
                           ) as target_percentage
                    FROM deduplicated_sales) t4 on t4.cust_code = t1.cust_code
         LEFT JOIN (select split_order_no            sales_order_code,
                           product_code              material_code,
                           split_order_line_no,
                           group_concat(outbound_no) shipment_code,
                           max(dbilldate)            invoiced_date,
                           max(outbound_date)        shipment_date,
                           sum(ship_quantity)        shipped_quantity,
                           sum(ifnull(nnum, 0))      invoiced_quantity,
                           sum(ifnull(norigmny, 0))  invoiced_amount
                    from bip_outbound_order_detail bood
                             join (select csrcid, csrcbid,dbilldate, sum(nnum) nnum, sum(norigmny) norigmny
                                   from crm_sales_invoice_details
                                   where flag_deleted = 0
                                   group by csrcid, csrcbid,dbilldate) csid
                                  on bood.outbound_header = csid.csrcid and bood.outbound_line_id = csid.csrcbid
                    where bood.flag_deleted = 0
                    group by sales_order_code, material_code, split_order_line_no) t5
                   on t5.sales_order_code = t1.sales_order_code and t2.material_code = t5.material_code
                       and t2.contract_product_line_number = t5.split_order_line_no
         left join metric_person t6
                   on t6.metric_year = date_format(t1.create_time, '%Y')
                       and t6.flag_deleted = 0
                       and t6.cust_manager_code = t4.sales_code
WHERE t1.flag_deleted = 0
  AND t2.flag_deleted = 0
  AND t3.flag_deleted = 0
  and t3.cust_status = 2
  and t3.cust_type not in (0, 1)
  AND t1.status not in (8)
  and ifnull(t5.invoiced_amount, 0)*t4.target_percentage/100>0

;
select * from dws_sales_complete_metric;
-- ETL建表
drop table dws_sales_complete_metric;
CREATE TABLE `dws_sales_complete_metric` (
                                        `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                        `sale_company` varchar(50) DEFAULT NULL COMMENT '销售公司',
                                        `deparment_code` varchar(50) DEFAULT NULL COMMENT '部门编码',
                                        `deparment_name` varchar(100) DEFAULT NULL COMMENT '部门名称',
                                        `region` varchar(50) DEFAULT NULL COMMENT '区域',
                                        `cust_code` varchar(50) DEFAULT NULL COMMENT '客户编码',
                                        `cust_name` varchar(200) DEFAULT NULL COMMENT '客户名称',
                                        `cust_manager_code` varchar(50) DEFAULT NULL COMMENT '销售人员编码',
                                        `cust_manager_name` varchar(50) DEFAULT NULL COMMENT '销售人员姓名',
                                        `sales_assistant_code` varchar(100) DEFAULT NULL COMMENT '销售助理编码',
                                        `sales_assistant_name` varchar(100) DEFAULT NULL COMMENT '销售助理姓名',
                                        `cust_type` tinyint DEFAULT NULL COMMENT '客户类别(0-新建客户,1-公海客户,2-合作客户,3-开发中客户,4-受限客户)',
                                        `cust_type_name` varchar(20) DEFAULT NULL COMMENT '客户类别名称',
                                        `sales_order_code` varchar(50) DEFAULT NULL COMMENT '拆分订单号',
                                        `create_time` date DEFAULT NULL COMMENT '下达日期',
                                        `factory_assigned` varchar(100) DEFAULT NULL COMMENT '下达工厂',
                                        `status` tinyint DEFAULT NULL COMMENT '拆分订单状态',
                                        `status_desc` varchar(20) DEFAULT NULL COMMENT '订单状态描述',
                                        `main_class` varchar(100) DEFAULT NULL COMMENT '产品大类',
                                        `sub_class` varchar(100) DEFAULT NULL COMMENT '产品小类',
                                        `material_code` varchar(50) DEFAULT NULL COMMENT '产品编码',
                                        `material_name` varchar(200) DEFAULT NULL COMMENT '产品名称',
                                        `product_quantity` decimal(10,2) DEFAULT NULL COMMENT '拆分订单数量',
                                        `unit_price_exclusive` decimal(12,6) DEFAULT NULL COMMENT '单价未税',
                                        `amount_exclusive_tax` decimal(12,2) DEFAULT NULL COMMENT '未税金额',
                                        `invoiced_quantity` decimal(10,2) DEFAULT NULL COMMENT '开票数量',
                                        `invoiced_amount` decimal(12,2) DEFAULT NULL COMMENT '开票金额',
                                        `sales_target` decimal(12,2) DEFAULT NULL COMMENT '销售目标',
                                        `completion_ratio` decimal(10,4) DEFAULT NULL COMMENT '完成比例',
                                        `contract_product_line_number` varchar(50) DEFAULT NULL COMMENT '合同产品行号',
                                        `invoiced_date` date DEFAULT NULL COMMENT '开票日期',
                                        PRIMARY KEY (`id`),
                                        KEY `idx_sales_order_code` (`sales_order_code`),
                                        KEY `idx_create_time` (`create_time`),
                                        KEY `idx_material_code` (`material_code`),
                                        KEY `idx_cust_code` (`cust_code`)
) COMMENT='销售指标完成报表';

-- ETL:CRM销售指标完成报表->dws_sales_complete_metric
select *
from cockpit.dws_sales_complete_metric
where
# if(:admin,
#          1,
#          if(:cust_code_size > 0, cust_code in (:cust_code_arr), 1)
#       )
#   AND ((:deparment_code IS NULL OR :deparment_code = '') OR (deparment_code = :deparment_code))
#   AND ((:region IS NULL OR :region = '') OR (region = :region))
#   AND ((:cust_code IS NULL OR :cust_code = '') OR (cust_code like concat('%', :cust_code, '%')))
#   AND ((:cust_name IS NULL OR :cust_name = '') OR (cust_name LIKE concat('%', :cust_name, '%')))
#   AND ((:material_code IS NULL OR :material_code = '') OR (material_code like concat('%', :material_code, '%')))
#   AND ((:material_name IS NULL OR :material_name = '') OR (material_name LIKE concat('%', :material_name, '%')))
#   AND ((:contract_start_date IS NULL OR :contract_start_date = '') OR
#        (create_time >= :contract_start_date))
#   AND ((:contract_end_date IS NULL OR :contract_end_date = '') OR
#        (create_time <= :contract_end_date))
#   AND ((:factory_assigned IS NULL OR :factory_assigned = '') OR (factory_assigned = :factory_assigned))
#   AND ((:sales_order_code IS NULL OR :sales_order_code = '') OR
#        (sales_order_code LIKE concat('%', :sales_order_code, '%')))
#   AND ((:invoiced_start_date IS NULL OR :invoiced_start_date = '') OR
#        (invoiced_date >= :invoiced_start_date))
#   AND ((:invoiced_end_date IS NULL OR :invoiced_end_date = '') OR
#        (invoiced_date <= :invoiced_end_date))
#   AND if(:cust_manager_size>0, cust_manager_code in (:cust_manager_arr), 1)
#   AND if(:sales_assistant_size>0,sales_assistant_code REGEXP :sales_assistant_str,1)
         sales_assistant_code REGEXP ''
ORDER BY id ASC
LIMIT :page_size offset :offset;

select sum(invoiced_amount) from dws_sales_complete_metric where invoiced_date>='2025-04-01' and invoiced_date<='2025-04-30'
