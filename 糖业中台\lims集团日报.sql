drop table if exists group_report_day;
CREATE TABLE `group_report_day` (
                                    `id` int auto_increment NOT NULL COMMENT '主键',
                                    `update_time` date  COMMENT '更新时间',
                                    `ftm_chanliang` varchar(255) default null comment '废糖蜜产量',
                                    `hunhetchanlv` varchar(255) default null comment '混合糖产率含在制品',
                                    `gs` varchar(255) default null comment '公司',
                                    PRIMARY KEY (`id`)
) COMMENT='集团日报_每天';
select * from group_report_day;

drop table if exists group_report_year;
CREATE TABLE `group_report_year` (
                                    `id` int auto_increment NOT NULL COMMENT '主键',
                                    `update_time` date  COMMENT '更新时间',
                                    `primary_juice_purity` varchar(255) default null comment '初压汁视纯度',
                                    `waste_molasses_yield` varchar(255) default null comment '废蜜产率',
                                    `steam_per_ton_cane` varchar(255) default null comment '吨蔗总用汽量',
                                    `waste_molasses_production` varchar(255) default null comment '废糖蜜产量',
                                    `water_per_ton_cane` varchar(255) default null comment '吨蔗用水量',
                                    `mixed_sugar_yield_with_wip` varchar(255) default null comment '混合糖产率含在制品',
                                    `avg_daily_equipment_capacity` varchar(255) default null comment '平均设备日榨量',
                                    `gs` varchar(255) default null comment '公司',
                                    PRIMARY KEY (`id`)
) COMMENT='集团日报_榨季';
select * from group_report_year;

SELECT avg_daily_equipment_capacity FROM group_report_year
WHERE update_time =CURRENT_DATE
;
