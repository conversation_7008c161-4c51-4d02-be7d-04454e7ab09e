-- 标准工时分析 etl
SELECT
    t4.job_bar,
    t2.machine_id,
    t3.machine_name,
    t1.production_batch_number AS production_batch_number,
    t1.product_name AS product_name,
    DATE(t2.planned_start_date) AS plan_date,
    SUM(t1.planned_time) AS planned_time,
    SUM(t1.preparation_time) AS preparation_time,
    ROUND(SUM(t4.actual_time) / 60, 2) AS actual_time,
    ROUND(SUM(t4.actual_time) / 60, 2) - SUM(t1.planned_time) AS diff_time
FROM
    cockpit.ods_pm_job_detail t1
        LEFT JOIN
    cockpit.ods_pm_job_association t2 ON t1.id = t2.job_id
        LEFT JOIN
    cockpit.ods_machine_file t3 ON t3.id = t2.machine_id
        LEFT JOIN
    cockpit.ods_pm_oee_records t4 ON t4.job_bar = t1.id
WHERE
    t1.flag_deleted = 0
  AND t2.flag_deleted = 0
  AND t4.flag_deleted = 0
and t1.create_time >=DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 1 DAY), '%Y-%m-%d 00:00:00')
and t1.create_time < DATE_FORMAT(CURDATE(), '%Y-%m-%d 00:00:00')
GROUP BY
    t4.job_bar,t2.machine_id, t1.production_batch_number, t1.product_name, DATE(t2.planned_start_date)
ORDER BY
    plan_date ASC, production_batch_number ASC

;

-- 今天 0 点
SELECT DATE_FORMAT(CURDATE(), '%Y-%m-%d 00:00:00') AS today_zero_time;

-- 昨天 0 点
SELECT DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 1 DAY), '%Y-%m-%d 00:00:00') AS yesterday_zero_time;

-- 查询实际准备时间
select ROUND(TIMESTAMPDIFF(minute, t1.debug_time, t2.production_time) / 60, 2) actual_preparation_time
from (select max(start_time) debug_time
      from cockpit.ods_pm_oee_records oee1
      where oee1.job_bar = :job_bar
        and action_properties = 6) t1,
     (select min(start_time) production_time
      from cockpit.ods_pm_oee_records oee2
      where oee2.job_bar = :job_bar
        and action_properties = 3) t2
;

-- 标准工时查询
select job_bar ,
       machine_id,
       machine_name,
       production_batch_number,
       product_name,
       plan_date,
       planned_time,
       preparation_time,
       actual_time,
       diff_time
from cockpit.dwd_standard_work_time t1
where ((:start_date IS NULL OR :start_date = '') OR (plan_date >= :start_date))
  AND ((:end_date IS NULL OR :end_date = '') OR (plan_date <= :end_date))
  AND ((:machine_name IS NULL OR :machine_name = '') OR (machine_name = :machine_name))
  AND ((:production_batch_number IS NULL OR :production_batch_number = '') OR
       (production_batch_number = :production_batch_number))
  AND ((:product_name IS NULL OR :product_name = '') OR (product_name = :product_name))
ORDER BY plan_date ASC, production_batch_number ASC
LIMIT :page_size OFFSET :offset
;

-- 实际准备工时
select TIMEDIFF(t1.debug_time,t2.production_time) from
(select max(start_time) debug_time from cockpit.ods_pm_oee_records
where job_bar=23788 and action_properties=6)t1,
(select max(start_time) production_time from cockpit.ods_pm_oee_records
where job_bar=23788 and action_properties=3)t2;


-- 折线图
select plan_date,
       sum(planned_time) planned_time,
       sum(actual_time) actual_time
from cockpit.dwd_standard_work_time t1
where ((:start_date IS NULL OR :start_date = '') OR (plan_date >= :start_date))
  AND ((:end_date IS NULL OR :end_date = '') OR (plan_date <= :end_date))
  AND ((:machine_name IS NULL OR :machine_name = '') OR (machine_name = :machine_name))
  AND ((:production_batch_number IS NULL OR :production_batch_number = '') OR
       (production_batch_number = :production_batch_number))
  AND ((:product_name IS NULL OR :product_name = '') OR (product_name = :product_name))
group by plan_date
order by plan_date asc
