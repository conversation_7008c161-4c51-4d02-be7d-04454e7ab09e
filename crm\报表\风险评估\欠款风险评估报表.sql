-- 欠款金额
select t2.cust_code,
       t2.cust_name,
       sum(qkbbbye) outstanding_amount,
       max(cast(qkts as decimal ))    outstanding_day
from crm_account_receivable_age t1
         left join (WITH RankedCustomers AS (SELECT *,
                                                    ROW_NUMBER() OVER (PARTITION BY cust_code ORDER BY update_time DESC) as rn
                                             FROM crm_cust_basic)
                    SELECT *
                    FROM RankedCustomers
                    WHERE rn = 1) t2 on t1.cust_code = t2.cust_code
where t1.flag_deleted = 0
  and t2.flag_deleted = 0
#   and date_format(t1.create_time, '%Y-%m') = DATE_FORMAT(DATE_SUB(CURRENT_DATE(), INTERVAL 1 MONTH), '%Y-%m')
  and date_format(t1.create_time, '%Y-%m') = DATE_FORMAT(current_date, '%Y-%m')
and t1.cust_name='武汉生物制品研究所有限责任公司'
group by cust_code, cust_name;

-- 欠款评分
select cust_code,
       cust_name,
       outstanding_amount,
       outstanding_day,
       risk_score,
       case
           when risk_score >= 36 then '重大风险'
           when risk_score >= 24 then '高风险'
           when risk_score >= 13 then '中等风险'
           when risk_score >= 7 then '一般'
           when risk_score >= 3 then '低风险'
           else '无风险' end                                               risk_level,
       DATE_FORMAT(DATE_SUB(CURRENT_DATE(), INTERVAL 1 MONTH), '%Y') AS data_year,
       DATE_FORMAT(DATE_SUB(CURRENT_DATE(), INTERVAL 1 MONTH), '%m') AS data_month,
       2                                                                type
from (
select t2.cust_code,
       t2.cust_name,
       round(sum(qkbbbye),2) outstanding_amount,
       max(qkts)    outstanding_day,
       CASE
           -- >=10万档位
           WHEN sum(qkbbbye) >= 100000 and max(qkts) > 270 THEN 36
           WHEN sum(qkbbbye) >= 100000 and max(qkts) >= 181 THEN 30
           WHEN sum(qkbbbye) >= 100000 and max(qkts) >= 121 THEN 24
           WHEN sum(qkbbbye) >= 100000 and max(qkts) >= 91 THEN 18
           WHEN sum(qkbbbye) >= 100000 and max(qkts) >= 61 THEN 12
           WHEN sum(qkbbbye) >= 100000 and max(qkts) >= 31 THEN 6

           -- 5-10万档位
           WHEN sum(qkbbbye) >= 50000 and max(qkts) > 270 THEN 30
           WHEN sum(qkbbbye) >= 50000 and max(qkts) >= 181 THEN 25
           WHEN sum(qkbbbye) >= 50000 and max(qkts) >= 121 THEN 20
           WHEN sum(qkbbbye) >= 50000 and max(qkts) >= 91 THEN 15
           WHEN sum(qkbbbye) >= 50000 and max(qkts) >= 61 THEN 10
           WHEN sum(qkbbbye) >= 50000 and max(qkts) >= 31 THEN 5

           -- 3-5万档位
           WHEN sum(qkbbbye) >= 30000 and max(qkts) > 270 THEN 24
           WHEN sum(qkbbbye) >= 30000 and max(qkts) >= 181 THEN 20
           WHEN sum(qkbbbye) >= 30000 and max(qkts) >= 121 THEN 16
           WHEN sum(qkbbbye) >= 30000 and max(qkts) >= 91 THEN 12
           WHEN sum(qkbbbye) >= 30000 and max(qkts) >= 61 THEN 8
           WHEN sum(qkbbbye) >= 30000 and max(qkts) >= 31 THEN 4

           ELSE 0
           END as risk_score
from h3chq_crmbusiness1704287359505.crm_account_receivable_age t1
         left join (WITH RankedCustomers AS (SELECT *,
                                                    ROW_NUMBER() OVER (PARTITION BY cust_code ORDER BY update_time DESC) as rn
                                             FROM h3chq_crmbusiness1704287359505.crm_cust_basic)
                    SELECT *
                    FROM RankedCustomers
                    WHERE rn = 1) t2 on t1.cust_code = t2.cust_code
where t1.flag_deleted = 0
  and t2.flag_deleted = 0
  and date_format(t1.create_time, '%Y-%m') = DATE_FORMAT(DATE_SUB(CURRENT_DATE(), INTERVAL 1 MONTH), '%Y-%m')
group by cust_code, cust_name
              )temp order by risk_score desc;


