-- MySQL dump 10.13  Distrib 8.4.4, for Win64 (x86_64)
--
-- Host: **************    Database: h3chq_pdmbusiness1704287270935
-- ------------------------------------------------------
-- Server version	8.0.29

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `api_log_record`
--

DROP TABLE IF EXISTS `api_log_record`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `api_log_record` (
  `record_flag` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'api_标志字段',
  `api_param` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT 'api参数',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=5516 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='api日志表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `pdm_accessories_subclass`
--

DROP TABLE IF EXISTS `pdm_accessories_subclass`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `pdm_accessories_subclass` (
  `area_unit` int DEFAULT NULL COMMENT '平方米产品(1.平方米2.平方厘米3.平方毫米4.个)',
  `product_num` decimal(10,2) DEFAULT NULL COMMENT '产品数量',
  `material_num` decimal(14,6) DEFAULT NULL COMMENT '物料数据',
  `count_mode` int DEFAULT NULL COMMENT '计算方式(1.按版面积2.按产品个数)',
  `units` int DEFAULT NULL COMMENT '单位',
  `status` int DEFAULT NULL COMMENT '状态(1.启用2.禁用)',
  `small_class` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '辅料小类',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  `weight_per_squarew` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '每平方重',
  `unit_price_square_meter` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '每平方单价',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `small_class` (`small_class`)
) ENGINE=InnoDB AUTO_INCREMENT=107 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='辅料小类设置';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `pdm_bom_recover_log_table`
--

DROP TABLE IF EXISTS `pdm_bom_recover_log_table`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `pdm_bom_recover_log_table` (
  `source_time` datetime DEFAULT NULL COMMENT '原数据创建时间',
  `material_name_json` text COLLATE utf8mb4_general_ci COMMENT '修复数据',
  `product_version` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '产品编码',
  `product_code` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '产品编码',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='index修复日志表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `pdm_edition`
--

DROP TABLE IF EXISTS `pdm_edition`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `pdm_edition` (
  `perimeter` int DEFAULT NULL COMMENT '版辊周长',
  `edition_cleanliness` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版净度/版幅度',
  `remark` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `edition_code` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版编号',
  `edition_type` int DEFAULT NULL COMMENT '版类型',
  `edition_categroy` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版类别',
  `edition_name` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版名称',
  `status` int DEFAULT NULL COMMENT '状态(0=生效，1=草稿，2=作废)',
  `source` int DEFAULT NULL COMMENT '来源(1=手工创建，2=产品档案，3=拼版通知单)',
  `share` int DEFAULT NULL COMMENT '共用(0否，1是)',
  `chromatic_degree` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '色次',
  `color_sequence` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '色序',
  `direction_mouth` int DEFAULT NULL COMMENT '叼口方向',
  `serviceability_ratio` decimal(20,10) DEFAULT NULL COMMENT '耐用率',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `edition_code` (`edition_code`) USING BTREE,
  KEY `edition_name` (`edition_name`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=134111 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='版档案';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `pdm_material`
--

DROP TABLE IF EXISTS `pdm_material`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `pdm_material` (
  `procure_unit_cn` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '尺寸单位 枚举',
  `standard_unit_cn` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '标准单位 枚举',
  `sub_class_cn` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '小类 枚举',
  `main_class_cn` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '大类 枚举',
  `gram_weight_cn` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '克重 枚举',
  `specification_cn` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '规格 枚举',
  `brand_cn` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '品牌 枚举',
  `material_weight` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '材质克重',
  `update_content` text CHARACTER SET utf8mb3 COLLATE utf8_general_ci COMMENT '更新内容',
  `is_support_rohs` int DEFAULT NULL COMMENT '是否ROHS',
  `fsc_statement` varchar(800) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'FSC声明',
  `is_support_fsc` int DEFAULT NULL COMMENT '是否FSC   0否，1是',
  `product_weight` decimal(12,6) DEFAULT NULL COMMENT '产品重量(Kg)',
  `spread_size` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '展开尺寸',
  `product_size` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '成品尺寸',
  `bom_type` int DEFAULT NULL COMMENT '档案类型   1、成本BOM，2、批量BOM，3、打样BOM',
  `is_group_product` int DEFAULT NULL COMMENT '组产品    0否，1是',
  `product_version` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本号',
  `default_factory` int DEFAULT NULL COMMENT '默认工厂',
  `safety_stock_material` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '安全库存物料  默认为0，0为不勾选，勾选为2',
  `procure_unit` int DEFAULT NULL COMMENT '尺寸单位    数据来源于参数设置',
  `remark` text CHARACTER SET utf8mb3 COLLATE utf8_general_ci COMMENT '描述/备注',
  `min_stock` decimal(20,10) DEFAULT NULL COMMENT '最小库存数量',
  `max_stock` decimal(20,10) DEFAULT NULL COMMENT '最大库存数量',
  `expiration_day` int DEFAULT NULL COMMENT '保质期天数',
  `standard_unit` int DEFAULT NULL COMMENT '标准单位   数据来源于参数设置',
  `sub_class` int DEFAULT NULL COMMENT '小类   数据来源于参数设置',
  `main_class` int DEFAULT NULL COMMENT '大类   数据来源于参数设置',
  `conversion_rate` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '折算率',
  `status` int DEFAULT NULL COMMENT '状态   1生效 2草稿(工艺未完善) 3审批中 4驳回',
  `category` int DEFAULT NULL COMMENT '集团分类/物料类别    1.纸张;2.辅料;3.包材;4.产品，5半成品;',
  `gram_weight` int DEFAULT NULL COMMENT '克重   数据来源于参数设置',
  `specification` int DEFAULT NULL COMMENT '规格    数据来源于参数设置',
  `brand` int DEFAULT NULL COMMENT '品牌    数据来源于参数设置',
  `material_name` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '物料名称',
  `material_code` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '物料编码',
  `mnemonic_code` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '助记码',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  `default_factory_cn` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '默认工厂枚举',
  `buy_unit_cn` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '采购单位  枚举',
  `buy_unit` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '采购单位',
  `prediction_type` int DEFAULT NULL COMMENT '预测类型(1、银卡2、国产纸3、进口纸)',
  `buy_price` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '采购价',
  `least_purchase` varchar(255) DEFAULT NULL COMMENT '最小采购量',
  `paper_full_name` varchar(100) DEFAULT NULL COMMENT '纸张全称',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `material_code_index` (`material_code`)
) ENGINE=InnoDB AUTO_INCREMENT=139929 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='物料档案';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `pdm_param_detail`
--

DROP TABLE IF EXISTS `pdm_param_detail`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `pdm_param_detail` (
  `remarks` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
  `status` int DEFAULT NULL COMMENT '状态(0=启用；1=禁用)',
  `parent_id` int DEFAULT NULL COMMENT '上级参数值',
  `detail_value` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '参数值',
  `detail_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '参数编号',
  `digit` int DEFAULT NULL COMMENT '小数点位数',
  `high` int DEFAULT NULL COMMENT '高',
  `wide` int DEFAULT NULL COMMENT '宽',
  `length` int DEFAULT NULL COMMENT '长',
  `info_id` int DEFAULT NULL COMMENT '参数设置id',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=27670 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='参数详情';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `pdm_param_info`
--

DROP TABLE IF EXISTS `pdm_param_info`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `pdm_param_info` (
  `type` int DEFAULT NULL COMMENT '参数类型(0普通，1规格)',
  `remarks` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
  `parent_id` int DEFAULT NULL COMMENT '上级参数项ID',
  `info_name` varchar(150) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '参数项名称',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  `source` int DEFAULT NULL COMMENT '来源(0:PDM1：ERP)',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `parent_id` (`parent_id`) USING BTREE,
  KEY `info_name` (`info_name`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=127 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='参数配置';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `pdm_parma_detail`
--

DROP TABLE IF EXISTS `pdm_parma_detail`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `pdm_parma_detail` (
  `enum_value` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '枚举值/小数位',
  `defaults` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '默认值',
  `edit_type` int DEFAULT NULL COMMENT '编辑框类型 1.下拉框2.文本框3.数字框4.勾选框',
  `requireds` int DEFAULT '1' COMMENT '必填值:默认true,1true,2false',
  `status` int DEFAULT NULL COMMENT '状态：1.启用2.禁用',
  `name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '名称',
  `process_code` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '工序设置主表code',
  `code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '编码',
  `type` int DEFAULT NULL COMMENT '分类(1工艺，2包装)',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=587 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='工艺or包装参数设置表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `pdm_process_consumption`
--

DROP TABLE IF EXISTS `pdm_process_consumption`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `pdm_process_consumption` (
  `process_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '工序设置编码',
  `min_consumption` int DEFAULT NULL COMMENT '最低运转消耗',
  `consumption_rate` decimal(10,5) DEFAULT NULL COMMENT '消耗率',
  `end_value` int DEFAULT NULL COMMENT '数量阶梯--结束数字',
  `start_value` int DEFAULT NULL COMMENT '数量阶梯--起始数字',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=206 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='工序运转消耗比例';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `pdm_process_setting`
--

DROP TABLE IF EXISTS `pdm_process_setting`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `pdm_process_setting` (
  `prepare_consumption` int DEFAULT NULL COMMENT '准备消耗(张)',
  `minimum_consumption` int DEFAULT NULL COMMENT '最低消耗(张)',
  `plate_type` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '默认关联版类型',
  `printing_operation` varchar(1) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '生成晒版作业(0否，1是)',
  `physical_version` varchar(1) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '关联实物版(0否，1是)',
  `display_panel` varchar(1) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '显示版(0否，1是)',
  `operation_unit` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '作业单位',
  `default_unit` varchar(200) DEFAULT NULL COMMENT '默认作业单位',
  `place_time` decimal(20,10) DEFAULT NULL COMMENT '半成品静置时间',
  `need_place` varchar(1) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '半成品静置是否开启(0否，1是)',
  `status` varchar(1) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '状态(0启用，1禁用，2全部)',
  `qa_require` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '质检要求',
  `process_type` varchar(1) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '工序类别(1大张工序，2小张工序)',
  `process_name` varchar(150) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '工序名称',
  `process_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '工序编码',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `process_code` (`process_code`) USING BTREE,
  KEY `process_name` (`process_name`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=115 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='工序设置';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `pdm_process_template`
--

DROP TABLE IF EXISTS `pdm_process_template`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `pdm_process_template` (
  `status` int DEFAULT NULL COMMENT '状态：1.启用2.禁用',
  `template_name` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '工艺模板名称',
  `template_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '工艺模板编码',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=38 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='工序模板';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `pdm_process_template_association`
--

DROP TABLE IF EXISTS `pdm_process_template_association`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `pdm_process_template_association` (
  `template_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '工序模板编码',
  `process_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '工序编码',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=95 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='工序模板绑定工序设置中间表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `pdm_product_ban_assunit`
--

DROP TABLE IF EXISTS `pdm_product_ban_assunit`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `pdm_product_ban_assunit` (
  `remark` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
  `serviceability_ratio` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '耐用率',
  `perimeter` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版辊周长',
  `edition_cleanliness` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版净度/版幅度',
  `direction_mouth` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '叼口方向',
  `chromatic_degree` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '色次',
  `color_sequence` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '色序',
  `source` int DEFAULT NULL COMMENT '来源(1=手工创建，2=产品档案，3=拼版通知单)',
  `share` int DEFAULT NULL COMMENT '共用(0否，1是)',
  `edition_type_cn` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版类型  枚举',
  `edition_categroy_cn` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版类别  枚举',
  `edition_type` int DEFAULT NULL COMMENT '版类型',
  `edition_name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版名称',
  `edition_categroy` int DEFAULT NULL COMMENT '版类别',
  `out_of_book` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '出本',
  `material_code` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '部件/产品编码',
  `edition_code` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版档案编码',
  `product_version` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '产品版本',
  `product_code` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '归属产品编码',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  `relevance_material_name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '关联辅料名称',
  `relevance_material_code` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '关联辅料编码',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `edition_code` (`edition_code`),
  KEY `material_code` (`material_code`),
  KEY `product` (`product_code`,`product_version`)
) ENGINE=InnoDB AUTO_INCREMENT=199221 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='产品版档案';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `pdm_product_bom`
--

DROP TABLE IF EXISTS `pdm_product_bom`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `pdm_product_bom` (
  `dosage_unit_unit_cn` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '单位用量单位',
  `packing_unit_cn` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '包装单位 枚举',
  `consume_unit_cn` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '消耗单位 枚举',
  `standard_unit_cn` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '尺寸单位 枚举',
  `product_count` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '产品数量',
  `bom_type` int DEFAULT NULL COMMENT '档案类型 1、成本BOM，2、批量BOM，3、打样BOM',
  `direct_material` int DEFAULT NULL COMMENT '直接物料',
  `indexs` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '物料顺位',
  `pack_paste_method` text CHARACTER SET utf8mb3 COLLATE utf8_general_ci COMMENT '包装其他粘贴方法',
  `pack_other_texture` text CHARACTER SET utf8mb3 COLLATE utf8_general_ci COMMENT '包装其他材质',
  `pack_background_color` text CHARACTER SET utf8mb3 COLLATE utf8_general_ci COMMENT '包装其他背景颜色',
  `pack_other_sizes` text CHARACTER SET utf8mb3 COLLATE utf8_general_ci COMMENT '包装其他尺寸',
  `box_mark_content` text CHARACTER SET utf8mb3 COLLATE utf8_general_ci COMMENT '箱签内容',
  `packing_parameter` longtext CHARACTER SET utf8mb3 COLLATE utf8_general_ci COMMENT '包装参数',
  `weight` decimal(20,10) DEFAULT NULL COMMENT '重(kg)包装',
  `packing_unit` int DEFAULT NULL COMMENT '包装单位',
  `ink_formulation` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '油墨配方',
  `consume_round_up` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '消耗取整',
  `consume_unit` int DEFAULT NULL COMMENT '消耗单位',
  `dosage_unit_unit` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '单位用量单位',
  `cut_size` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '分切尺寸',
  `notes_on_cutting` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '分切备注',
  `slitting` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '分切',
  `cut_remark` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '裁切备注',
  `pushing` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '开法',
  `is_off_side` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '偏开',
  `consumption_rate` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '消耗率',
  `dosage_unit` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '单位用量',
  `fixed_amount` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '固定用量',
  `product_version` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '产品版本',
  `product_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '归属产品编码',
  `parent_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '父级编码',
  `makeup_product` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '拼版主产品',
  `remark` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
  `part_information` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '部件信息',
  `color_order_quotation` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '色序报价',
  `color_sequence` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '色序',
  `chromatic_degree` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '色次',
  `standard_unit` int DEFAULT NULL COMMENT '尺寸单位',
  `spread_size` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '展开尺寸',
  `component_size` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '尺寸',
  `component_count` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '部件数量',
  `material_name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '物料/部件名称',
  `mnemonic_code` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '助记码',
  `material_code` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '物料/部件编码',
  `categroy` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'BOM类别(0部件，1纸张，2辅料，3产品，4包装，5子包装，6包材，7子产品,8托盘)',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  `gram_weight_cn` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '纸张克重',
  `mine_version` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '组产品的子产品版本',
  `children_version` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '子产品大版本（与子产品编号按顺序对应）',
  `children_product` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '子产品编码',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `material_code_product_version` (`material_code`,`product_version`),
  KEY `idx_pdm_product_bom_code_version` (`product_code`,`product_version`)
) ENGINE=InnoDB AUTO_INCREMENT=978232 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='产品BOM明细表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `pdm_product_client`
--

DROP TABLE IF EXISTS `pdm_product_client`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `pdm_product_client` (
  `unit_cn` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '单位枚举值',
  `is_used` int DEFAULT NULL COMMENT '是否禁用(0=禁用；1=激活)',
  `unit` int DEFAULT NULL COMMENT '单位',
  `grade_name` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '客户品名',
  `object_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '客户料号',
  `client_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '客户编码',
  `client_name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '客户名称',
  `mnecode` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '助记码',
  `product_version` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '产品版本',
  `product_code` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '归属产品编码',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  `customer_general_requirements` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '客户通用需求',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `client_code` (`client_code`),
  KEY `product` (`product_version`,`product_code`),
  KEY `idx_pdm_product_client_product` (`product_code`)
) ENGINE=InnoDB AUTO_INCREMENT=93964 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='客户料号';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `pdm_product_craft`
--

DROP TABLE IF EXISTS `pdm_product_craft`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `pdm_product_craft` (
  `source_name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '资源名称',
  `craft_name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '工艺名称',
  `technological_parameter` longtext CHARACTER SET utf8mb3 COLLATE utf8_general_ci COMMENT '工艺参数',
  `material_name` longtext CHARACTER SET utf8mb3 COLLATE utf8_general_ci COMMENT '绑定物料(inedx)',
  `parent_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '产品/部件编码',
  `ys_manual_speed` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '手工速度',
  `ys_standing_time` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '停留时间',
  `rate` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '难度系数',
  `fixed_consumption` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '固定消耗',
  `source_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '资源中心编码',
  `craft_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '工艺编码',
  `ys_fixed_rate` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '用时难度系数',
  `ys_fixed_time` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '用时固定用时',
  `indexs` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '工序顺位',
  `ban_json_code` longtext CHARACTER SET utf8mb3 COLLATE utf8_general_ci COMMENT '绑定版',
  `material_json_code` longtext CHARACTER SET utf8mb3 COLLATE utf8_general_ci COMMENT '绑定物料',
  `product_version` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '产品版本',
  `product_code` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '归属产品编码',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  `out_of_book` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '出本数',
  `count` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '系数',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `craft_code` (`craft_code`),
  KEY `product` (`product_code`,`product_version`),
  KEY `source_code` (`source_code`),
  KEY `idx_pdm_product_craft_code_version` (`product_code`,`product_version`)
) ENGINE=InnoDB AUTO_INCREMENT=575158 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='产品关联工艺路线';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `pdm_product_version`
--

DROP TABLE IF EXISTS `pdm_product_version`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `pdm_product_version` (
  `component_count` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '数量',
  `default_factory_cn` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '默认工厂枚举',
  `default_factory` int DEFAULT NULL COMMENT '默认工厂',
  `standard_unit_cn` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '产品单位枚举',
  `procure_unit_cn` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '尺寸单位枚举',
  `sub_class_cn` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '小类枚举',
  `main_class_cn` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '大类枚举',
  `recovery_time` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '恢复锁定时间',
  `status` int DEFAULT NULL COMMENT '状态  1、生效，2、工艺未完善(草稿)，3、审批中，4、驳回，5、归档，6、锁定',
  `update_content` text CHARACTER SET utf8mb3 COLLATE utf8_general_ci COMMENT '更新内容',
  `material_weight` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '材质克重',
  `is_support_rohs` int DEFAULT NULL COMMENT '是否ROHS(1是，0否)',
  `fsc_statement` varchar(800) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'FSC声明',
  `is_support_fsc` int DEFAULT NULL COMMENT '是否FSC(1是，0否)',
  `product_weight` decimal(12,6) DEFAULT NULL COMMENT '产品重量(Kg)',
  `spread_size` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '展开尺寸',
  `product_size` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '成品尺寸',
  `bom_type` int DEFAULT NULL COMMENT '档案类型   1、成本BOM，2、批量BOM，3、打样BOM',
  `product_version` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本号',
  `is_group_product` int DEFAULT NULL COMMENT '组产品（1是，0否）',
  `standard_unit` int DEFAULT NULL COMMENT '产品单位',
  `procure_unit` int DEFAULT NULL COMMENT '尺寸单位',
  `remark` text CHARACTER SET utf8mb3 COLLATE utf8_general_ci COMMENT '备注',
  `main_class` int DEFAULT NULL COMMENT '大类',
  `sub_class` int DEFAULT NULL COMMENT '小类',
  `material_name` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '产品名称',
  `mnemonic_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '助记码',
  `material_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '产品编码',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  `product_approval` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '审批原因',
  `crm_status` char(1) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '自动生成核价单状态0未生成1已生成3生成失败',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `mnemonic_code` (`mnemonic_code`) USING BTREE,
  KEY `material_name` (`material_name`) USING BTREE,
  KEY `material_code` (`material_code`) USING BTREE,
  KEY `material_code_product_version` (`material_code`,`product_version`),
  KEY `idx_pdm_product_version_deleted_status_id` (`flag_deleted`,`status`,`id`),
  KEY `idx_pdm_product_version_search_ext` (`flag_deleted`,`status`,`material_code`,`product_version`,`is_group_product`,`create_time`,`update_time`)
) ENGINE=InnoDB AUTO_INCREMENT=158851 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='产品版本表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `pdm_record_detail`
--

DROP TABLE IF EXISTS `pdm_record_detail`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `pdm_record_detail` (
  `table_id` int DEFAULT NULL COMMENT '操作表id',
  `record_table` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '操作表名',
  `remark` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
  `message` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '操作内容',
  `deparment_name` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '部门名称',
  `deparment` int DEFAULT NULL COMMENT '部门id',
  `create_by_name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人名称',
  `update_by_name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人名称',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=5800 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='操作记录';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `pdm_resource_manager`
--

DROP TABLE IF EXISTS `pdm_resource_manager`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `pdm_resource_manager` (
  `manufacturing_expense_json` text CHARACTER SET utf8mb3 COLLATE utf8_general_ci COMMENT '制造费用项json',
  `manufacturing_expense_costs` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '制造费用成本',
  `labor_hour_costs` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '人工工时成本',
  `remark` text CHARACTER SET utf8mb3 COLLATE utf8_general_ci COMMENT '备注',
  `status` int DEFAULT NULL COMMENT '状态(1启用 2禁用)',
  `resource_name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '资源名称',
  `resource_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '资源编码',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  `is_row_teeth` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '是否牙排 1是2否',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `resource_code` (`resource_code`)
) ENGINE=InnoDB AUTO_INCREMENT=148 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='资源管理';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `pdm_resource_process`
--

DROP TABLE IF EXISTS `pdm_resource_process`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `pdm_resource_process` (
  `resource_code` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '资源中心编码',
  `defuat_unit` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '额定单位',
  `standard_speed` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '标准速度',
  `fixed_hours` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '固定准备小时',
  `process_code` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '工序编码',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `process_code` (`process_code`),
  KEY `resource_code` (`resource_code`)
) ENGINE=InnoDB AUTO_INCREMENT=415 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='资源中心关联工序';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `pdm_resource_work`
--

DROP TABLE IF EXISTS `pdm_resource_work`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `pdm_resource_work` (
  `factory_owned` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '所属工厂(1、凌峰MOM，2、天津方正)',
  `work_code` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '工作中心编码',
  `resource_code` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '资源中心编码',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `resource_code` (`resource_code`),
  KEY `work_code` (`work_code`)
) ENGINE=InnoDB AUTO_INCREMENT=547 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='资源中心关联工作中心';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `pdm_upload_file`
--

DROP TABLE IF EXISTS `pdm_upload_file`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `pdm_upload_file` (
  `product_version` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '产品版本',
  `file_url` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '文件路径',
  `material_code` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '唯一归属编码',
  `type` int DEFAULT NULL COMMENT '类型 1、版档案，2、纸张，3、 辅料，4、包材，5、产品',
  `file_name` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '文件名称',
  `file_id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '文件id',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  `file_type` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '类型 1图片，2附件',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `material_code` (`material_code`) USING BTREE,
  KEY `idx_pdm_upload_file_material_version` (`material_code`,`product_version`)
) ENGINE=InnoDB AUTO_INCREMENT=29760 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='文件表';
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-07-04 18:30:54
