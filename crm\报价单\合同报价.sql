-- 报价发送OA日志
select *
from crm_record cr
where logic = 'OA审批'
  and paramL like '%BJ2503000034%';
select cust_code, cust_name,oa_id,cust_manager_code,cust_manager_name
from crm_preliminary_quotation cpq
where cust_manager_name ='';

select t1.status, t1.* from crm_preliminary_quotation t1
where preliminary_quotation_code = 'BJ2507000280';
select * from crm_record where logic = 'OA审批' and paramL like '%BJ2502000545%';
select *
from crm_preliminary_quotation cpq
where preliminary_quotation_code = '25060242A';
SELECT cqp.preliminary_quotation_code
FROM crm_preliminary_quotation cqp
WHERE cqp.flag_deleted = 0
  AND cqp.oa_id = 196249;

select cust_manager_name,cust_manager_code,ccmp.*
from crm_contract_management ccmp
where cust_manager_name ='';

-- 报价单产品
select *
from crm_preliminary_quotation_product cpqp
where preliminary_quotation_code = 'BJ2507000250';
SELECT cqp.preliminary_quotation_code
FROM crm_preliminary_quotation cqp
WHERE cqp.flag_deleted = 0
  AND cqp.oa_id = 196859;
select *
from crm_preliminary_quotation
where preliminary_quotation_code = 'BJ2507000280';

select *
from crm_preliminary_quotation cpq
where oa_id is not null;

select t1.oa_id oa_id
from crm_preliminary_quotation t1
         left join crm_contract_management t2 on
    t1.preliminary_quotation_code = t2.quotation_code
where t1.oa_id is not null
  and t2.id is null
;

select * from crm_sales_order cso where sales_order_code='25060242A';
select * from crm_sales_order_product  cso where sales_order_code='25060242A';
select *
from hq_mdm_b.mdm_mom_afferent mma
where module_name in ('报价合同评审')
-- and JSON_EXTRACT(data ,'$.id')='183791'
;
select *
from hq_mdm_b.mdm_mom_afferent mma
where module_name in ('报价合同评审')
  and JSON_EXTRACT(data ,'$.id')= '183791';

select *
from crm_quotation
where id in (36923, 37323, 38314, 38317, 38328);


select distinct
    cpq.id,
    if(cpq.imposition_quotation = "是" ,
       (select
            GROUP_CONCAT(cpqc.cust_manager_name separator ',')
        from
            crm_preliminary_quotation_cust cpqc
        where
            cpqc.flag_deleted = 0
          and cpqc.preliminary_quotation_code = cpq.preliminary_quotation_code
        group by
            cpqc.preliminary_quotation_code ),ccb.cust_manager_name) as cust_manager_name,
    IF(cpq.imposition_quotation = "是",
       (SELECT GROUP_CONCAT(DISTINCT ccb_inner.sales_assistant_code SEPARATOR ',')
        FROM crm_cust_basic ccb_inner
                 JOIN crm_preliminary_quotation_cust cpqc ON cpqc.cust_code = ccb_inner.cust_code
        WHERE ccb_inner.flag_deleted = 0
          AND cpqc.preliminary_quotation_code = cpq.preliminary_quotation_code
          AND ccb_inner.cust_version = 1
        GROUP BY cpqc.preliminary_quotation_code), ccb.sales_assistant_code) AS sales_assistant_code,
    cpq.delivery_address,
    cpq.total_cost,
    cpq.remark,
    cpq.type,
    cpq.tax_rate,
    cpq.show_update_by,
    cpq.total_sales_gross_profit,
    cpq.create_by,
    cpq.update_time,
    cpq.deparment,
    if(cpq.imposition_quotation = "是" ,
       (select GROUP_CONCAT(cpqc.cust_manager_code separator ',')
        from crm_preliminary_quotation_cust cpqc
        where
            cpqc.flag_deleted = 0
          and cpqc.preliminary_quotation_code = cpq.preliminary_quotation_code
        group by
            cpqc.preliminary_quotation_code ),ccb.cust_manager_code) as cust_manager_code,
    cpq.depreciation_expense,
    if(cpq.imposition_quotation = "是" ,
       (
           select
               GROUP_CONCAT(cpqc.cust_name separator ',')
           from
               crm_preliminary_quotation_cust cpqc
           where
               cpqc.flag_deleted = 0
             and cpqc.preliminary_quotation_code = cpq.preliminary_quotation_code
           group by
               cpqc.preliminary_quotation_code ),
       cpq.cust_name) as cust_name,
    cpq.id,
    cpq.total_sales,
    cpq.update_by,
    cpq.flag_deleted,
    cpq.utilities_labor,
    cpq.overall_sales_margin_rate,
    cpq.quotation_factory,
    cpq.exchange_rate,
    cpq.create_time,
    cpq.show_create_by,
    cpq.preliminary_quotation_code,
    cpq.last_year_margin_rate,
    if(cpq.imposition_quotation = "是" ,
       (
           select
               GROUP_CONCAT(cpqc.cust_vip separator ',')
           from
               crm_preliminary_quotation_cust cpqc
           where
               cpqc.flag_deleted = 0
             and cpqc.preliminary_quotation_code = cpq.preliminary_quotation_code
           group by
               cpqc.preliminary_quotation_code ),
       cpq.cust_vip) as cust_vip,
    cpq.freight_cost,
    cpq.settlement_currency,
    if(cpq.imposition_quotation = "是" ,
       (
           select
               GROUP_CONCAT(cpqc.cust_code separator ',')
           from
               crm_preliminary_quotation_cust cpqc
           where
               cpqc.flag_deleted = 0
             and cpqc.preliminary_quotation_code = cpq.preliminary_quotation_code
           group by
               cpqc.preliminary_quotation_code ),
       cpq.cust_code) cust_code,
    cpq.delivery_date,
    cpq.imposition_quotation,
    cpq.status,
    case
        cpq.status when 0 then '草稿'
                   when 1 then '生效'
                   when 2 then '审批中'
                   when 3 then '驳回'
                   when 4 then '生效'
                   when 5 then '作废'
                   when 6 then '完成'
        end as status_desc,
    case
        cpq.cust_vip when 1 then 'A'
                     when 2 then 'B'
                     when 3 then 'C'
                     when '' then '无效'
        end as cust_vip_desc,
    (
        select
            group_concat(quotation_code)
        from
            crm_preliminary_quotation_product cpqp
        where
            cpqp.flag_deleted = 0
          and cpqp.preliminary_quotation_code = cpq.preliminary_quotation_code
        group by
            cpqp.preliminary_quotation_code ) as quotation_code,
    (
        select
            group_concat(contract_management_code)
        from
            crm_contract_management ccm
        where
            ccm.flag_deleted = 0
          and ccm.quotation_code = cpq.preliminary_quotation_code
        group by
            ccm.quotation_code ) as contract_management_code
from
    crm_preliminary_quotation cpq
        left join crm_preliminary_quotation_product cpqp on
        cpq.preliminary_quotation_code = cpqp.preliminary_quotation_code
            and cpqp.flag_deleted = 0
        left join crm_preliminary_quotation_cust cpqc on
        cpq.preliminary_quotation_code = cpqc.preliminary_quotation_code
            and cpqc.flag_deleted = 0
        left join crm_cust_basic ccb on
        ccb.flag_deleted = 0
            and if(cpq.imposition_quotation = "是", cpqc.cust_code = ccb.cust_code, cpq.cust_code = ccb.cust_code)
            AND ccb.cust_type not in ('0','1')
            AND ccb.cust_status in ('2','4')

where
    cpq.flag_deleted = 0
  and ((:preliminary_quotation_code is null or :preliminary_quotation_code = '')
    or (cpq.preliminary_quotation_code like concat('%',:preliminary_quotation_code, '%')) )
;

select * from crm_contract_management ccm where quotation_code='BJ2507000196';
select * from crm_preliminary_quotation cpq where preliminary_quotation_code='BJ2507000196';

select
    cpq.delivery_address,
    cpq.total_cost,
    cpq.remark,
    cpq.type,
    cpq.tax_rate,
    cpq.show_update_by,
    cpq.total_sales_gross_profit,
    cpq.create_by,
    cpq.update_time,
    cpq.quotation_code,
    cpq.deparment,
    cpq.cust_manager_code,
    cpq.depreciation_expense,
    cpq.cust_name,
    cpq.id,
    cpq.total_sales,
    cpq.update_by,
    cpq.flag_deleted,
    cpq.utilities_labor,
    cpq.overall_sales_margin_rate,
    cpq.quotation_factory,
    cpq.exchange_rate,
    cpq.create_time,
    cpq.show_create_by,
    cpq.preliminary_quotation_code,
    cpq.last_year_margin_rate,
    cpq.cust_vip,
    cpq.freight_cost,
    cpq.settlement_currency,
    cpq.cust_code,
    cpq.delivery_date,
    cpq.imposition_quotation,
    cpq.cust_manager_name,
    cpq.status,
    case
        cpq.status
        when 0 then '草稿'
        when 1 then '生效'
        when 2 then '审批中'
        when 3 then '驳回'
        when 4 then '生效'
        when 5 then '作废'
        end as status_desc,
    case
        cpq.cust_vip
        when 1 then 'A'
        when 2 then 'B'
        when 3 then 'C'
        when '' then '无效'
        end as cust_vip_desc,
    cpq.one_expenses,
    cpq.print_count,
    ccb.cust_status,
    cct.street as administrative_code,
    cct.contact_phone
from
    crm_preliminary_quotation cpq
        left join crm_cust_basic ccb on ccb.cust_code = cpq.cust_code and ccb.flag_deleted = 0
        left join crm_cust_transport cct on cct.cust_mnemonic_code = ccb.cust_mnemonic_code and cct.flag_deleted = 0
        AND cct.is_default = '0'
where
    cpq.flag_deleted = 0
  and ((:preliminary_quotation_code is null
    or :preliminary_quotation_code = '')
    or (cpq.preliminary_quotation_code = :preliminary_quotation_code) )
;
select * from crm_contract_management ccm where quotation_code='BJ2507000093';
