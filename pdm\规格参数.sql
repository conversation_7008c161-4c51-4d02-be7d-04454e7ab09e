select *
from pdm_param_info ppi
where info_name = '规格'
  and flag_deleted = 0;
-- 查询所有单位
select ppd.*,ppi.id as ppi_id
from pdm_param_info ppi
         join pdm_param_detail ppd on ppi.id = ppd.info_id
where ppi.info_name = '规格'
  and ppd.detail_value='9*9*9'
  and ppi.flag_deleted = 0
  and ppd.flag_deleted = 0
;
-- 查询缺失
select standard_unit,material_name,material_code from pdm_product_version ppv where standard_unit is null;
-- 查询不在的id
select standard_unit
from pdm_product_version ppv
where standard_unit not in
      (select ppd.id
       from pdm_param_info ppi
                join pdm_param_detail ppd on ppi.id = ppd.info_id
       where ppi.info_name = '标准单位'
         and ppi.flag_deleted = 0
         and ppd.flag_deleted = 0);
