-- 传出中间表
SELECT
	mmo.id,
	mmo.document_Number,
	mmo.`data`,
	mmo.msg,
	mmo.state,
	CASE
		WHEN mmo.state = 0 THEN '未消费'
		WHEN mmo.state = 1 THEN '已消费'
		ELSE mmo.state
	END state_name,
	mmo.create_time,
	mmo.update_time
FROM
	hq_mdm_b.mdm_mom_outgoing mmo
WHERE
	mmo.flag_deleted = 0
	AND mmo.data_Sources = '销售订单新增'
	AND mmo.`data` LIKE '%25070172A%'
ORDER BY
	id DESC
LIMIT 50;
select * from hq_mdm_b.test_llw tl where t_data like '%25070172A%';


-- 传入中间表
SELECT
	mmo.id,
	mmo.module_name,
	mmo.`data`,
	mmo.msg,
	mmo.state,
	CASE
		WHEN mmo.state = 0 THEN '未消费'
		WHEN mmo.state = 1 THEN '已消费'
		ELSE mmo.state
	END state_name,
	mmo.create_time
FROM
	hq_mdm_b.mdm_mom_afferent mmo
WHERE
	mmo.flag_deleted = 0
	AND mmo.module_name LIKE '%销售订单%'
	AND json_extract( mmo.`data`,'$.erp_production_order') = '25070172A'
ORDER BY
	id DESC
LIMIT 50;


-- crm销售订单与mom关联信息
SELECT
	cso.id,
	csop.id,
	cso.sales_order_code 'crm销售订单号',
	cso.csaleorderid 'crm推送bip返回主表主键,bip_main_no',
	pso.bip_main_no 'mom接收bip订单主键',
	csop.csaleorderbid 'crm推送bip返回明细主键,info.bip_detail_no',
	pop.bip_detail_no 'mom接收bip订单明细主键',
	csop.material_code 'crm产品编码',
	pop.product_number 'mom产品编码',
	pso.order_number 'mom销售订单号（bip销售订单号）',
	pso.erp_production_order,
	cso.create_time
FROM
	h3chq_crmbusiness1704287359505.crm_sales_order cso
LEFT JOIN h3chq_crmbusiness1704287359505.crm_sales_order_product csop ON
	csop.sales_order_code = cso.sales_order_code
	AND csop.flag_deleted = 0
LEFT JOIN hq_pm_b.pm_sale_order pso ON
	pso.bip_main_no = cso.csaleorderid
	AND pso.flag_deleted = 0
LEFT JOIN hq_pm_b.pm_order_product pop ON
	pop.sales_number = pso.order_number
	AND pop.product_number = csop.material_code
	AND pop.flag_deleted = 0
WHERE
	cso.flag_deleted = 0
# and cso.csaleorderid='1001A5100000012EF1CQ'
	AND cso.sales_order_code IN ('25070172A');

;
-- crm缺失数据补全 bip_main_no
select
    cso.id,
    cso.sales_order_code ,
    cso.csaleorderid,
    cso.flag_deleted
    from h3chq_crmbusiness1704287359505.crm_sales_order cso
    where sales_order_code IN ('25070172A');
-- crm info.bip_detail_no
select id,material_code,csaleorderbid,flag_deleted,contract_product_line_number
from h3chq_crmbusiness1704287359505.crm_sales_order_product csop
where sales_order_code IN ('25070172A');



-- 生产中 销售订单和生产订单关联
SELECT
	pop.bip_detail_no factory_details_id,
	COALESCE(pso.plan_delivery_date, '') delivery_date,
	pjo.production_batch_number,
	pop.product_number,
	0 stock_quantity,
	pjo.version,
	pop.id,
	pjo.id,
	pjo.source_detail_id,
	pop.product_version,
	pso.bip_main_no
FROM
	hq_pm_b.pm_sale_order pso
LEFT JOIN hq_pm_b.pm_order_product pop ON
	pop.sales_order_id = pso.id
LEFT JOIN hq_pm_b.pm_job_order pjo ON
	pjo.order_code = pso.order_number
	AND pjo.material_code = pop.product_number
# 	AND pjo.version = pop.product_version  -- 查询作业条等信息条件
WHERE
	pso.flag_deleted = 0
	AND pop.flag_deleted = 0
-- 	AND pop.bip_detail_no IN ('1001A510000000C5NV0A')
	AND pso.erp_production_order LIKE '25060249A';
select t1.id,t1.* from hq_pm_b.pm_sale_order t1 where erp_production_order like '25070172A';
select * from hq_pm_b.pm_order_product where sales_order_id=5093;
select * from hq_pm_b.pm_job_order where sales_order_id=7490;





