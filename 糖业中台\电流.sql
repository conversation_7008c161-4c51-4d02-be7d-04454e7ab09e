-- 电流指标
drop table ods_electric_current_day;
create table ods_electric_current_day
(
    id             int auto_increment primary key,
    `device_name`   varchar(100) comment '设备名称',
    `metric_value` varchar(1000) comment '指标数据',
    update_time    datetime default current_timestamp comment '更新时间',
    remark          varchar(100) comment '备注',
    index device_num_index (device_name)
) comment '设备电流指标';

select current_date date_str;
select * from ods_electric_current_day oecd;


SELECT id,name,date,value,CURRENT_DATE() as dat
FROM sugar_key_index_r3
WHERE name='R3蜜量'
  and date is null;

select * from sugar_key_index_r3 WHERE name='R3蜜量';
select current_date;
update sugar_key_index_r3 set date=current_date() where name='R3蜜量'  and date is null;
