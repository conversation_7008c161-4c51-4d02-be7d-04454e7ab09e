SELECT m.year_num,                 -- 年份
       m.month_num AS month,       -- 月份
       k.kind_name,                -- 分类名称
       COALESCE(
               ROUND(
                       (COALESCE(md.defect_count, 0) / NULLIF(td.total_defects, 0)) * 100,
                       2
               ),
               0
       )           AS defect_ratio -- 缺陷占比
FROM (SELECT YEAR(CURDATE()) AS year_num, 1 AS month_num
      UNION ALL
      SELECT YEAR(CURDATE()) AS year_num, 2
      UNION ALL
      SELECT YEAR(CURDATE()) AS year_num, 3
      UNION ALL
      SELECT YEAR(CURDATE()) AS year_num, 4
      UNION ALL
      SELECT YEAR(CURDATE()) AS year_num, 5
      UNION ALL
      SELECT YEAR(CURDATE()) AS year_num, 6
      UNION ALL
      SELECT YEAR(CURDATE()) AS year_num, 7
      UNION ALL
      SELECT YEAR(CURDATE()) AS year_num, 8
      UNION ALL
      SELECT YEAR(CURDATE()) AS year_num, 9
      UNION ALL
      SELECT YEAR(CURDATE()) AS year_num, 10
      UNION ALL
      SELECT YEAR(CURDATE()) AS year_num, 11
      UNION ALL
      SELECT YEAR(CURDATE()) AS year_num, 12
      ) AS m
         CROSS JOIN (SELECT kind_name,
                            COUNT(*) AS total_defects
                     FROM ods_waste_disposal_item wd
                              JOIN ods_waste_disposal wd_disp ON wd.job_detail_id = wd_disp.id
                     WHERE wd_disp.flag_deleted = 0
                       and kind_name IS NOT NULL
                       and date_format(wd.create_time, '%Y-%m') = date_format(curdate(), '%Y-%m')
                     group by process_name
                     order by total_defects desc
                     limit 10) AS k
         LEFT JOIN (SELECT YEAR(wd.create_time)  AS year_num,
                           MONTH(wd.create_time) AS month_num,
                           wd.kind_name,
                           COUNT(*)              AS defect_count
                    FROM ods_waste_disposal_item wd
                             JOIN ods_waste_disposal wd_disp ON wd.job_detail_id = wd_disp.id
                    WHERE wd_disp.flag_deleted = 0
                      AND wd.kind_name IS NOT NULL -- 确保只统计有分类的缺陷数据
                    GROUP BY year_num, month_num, wd.kind_name) AS md
                   ON m.year_num = md.year_num
                       AND m.month_num = md.month_num
                       AND k.kind_name = md.kind_name
         LEFT JOIN (SELECT YEAR(wd.create_time)  AS year_num,
                           MONTH(wd.create_time) AS month_num,
                           COUNT(*)              AS total_defects
                    FROM ods_waste_disposal_item wd
                             JOIN ods_waste_disposal wd_disp ON wd.job_detail_id = wd_disp.id
                    WHERE wd_disp.flag_deleted = 0
                    GROUP BY year_num, month_num) AS td
                   ON m.year_num = td.year_num
                       AND m.month_num = td.month_num
ORDER BY m.year_num, m.month_num,defect_ratio desc;

-- top10
SELECT kind_name,
       COUNT(*) AS total_defects
FROM ods_waste_disposal_item wd
         JOIN ods_waste_disposal wd_disp ON wd.job_detail_id = wd_disp.id
WHERE wd_disp.flag_deleted = 0
  and date_format(wd.create_time, '%Y-%m') = date_format(curdate(), '%Y-%m')
group by process_name
order by total_defects desc
limit 10;

UNION ALL
SELECT YEAR(CURDATE()) - 1 AS year_num, 1
UNION ALL
SELECT YEAR(CURDATE()) - 1 AS year_num, 2
UNION ALL
SELECT YEAR(CURDATE()) - 1 AS year_num, 3
UNION ALL
SELECT YEAR(CURDATE()) - 1 AS year_num, 4
UNION ALL
SELECT YEAR(CURDATE()) - 1 AS year_num, 5
UNION ALL
SELECT YEAR(CURDATE()) - 1 AS year_num, 6
UNION ALL
SELECT YEAR(CURDATE()) - 1 AS year_num, 7
UNION ALL
SELECT YEAR(CURDATE()) - 1 AS year_num, 8
UNION ALL
SELECT YEAR(CURDATE()) - 1 AS year_num, 9
UNION ALL
SELECT YEAR(CURDATE()) - 1 AS year_num, 10
UNION ALL
SELECT YEAR(CURDATE()) - 1 AS year_num, 11
UNION ALL
SELECT YEAR(CURDATE()) - 1 AS year_num, 12
