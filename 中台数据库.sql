-- 产值产量按产品大类月份统计
CREATE TABLE dwd_production_value_month
(
    id                        INT AUTO_INCREMENT PRIMARY KEY COMMENT 'id',
    product_big_category_name VARCHAR(255)   NOT NULL COMMENT '产品大类中文名称',
    production_value          DECIMAL(15, 2) NOT NULL COMMENT '产值',
    production_value_yoy      DECIMAL(15, 2) NOT NULL COMMENT '产值同比',
    production_value_mom      DECIMAL(15, 2) NOT NULL COMMENT '产值环比',
    production_quantity       DECIMAL(15, 2) NOT NULL COMMENT '产量',
    production_quantity_yoy   DECIMAL(15, 2) NOT NULL COMMENT '产量同比',
    production_quantity_mom   DECIMAL(15, 2) NOT NULL COMMENT '产量环比',
    update_month              varchar(50)    NOT NULL COMMENT '更新月份yyyy-MM'
) comment '产值产量按产品大类月份统计';

-- 产品大类维度表
CREATE TABLE dim_product_big_category
(
    id   INT AUTO_INCREMENT PRIMARY KEY COMMENT 'id',
    code VARCHAR(255) NOT NULL COMMENT '产品大类编码',
    name VARCHAR(255) NOT NULL COMMENT '产品大类中文名称'
) comment '产品大类维度表';

-- 周转天数按产品大类月份统计
CREATE TABLE dwd_turnover_month
(
    id                        INT AUTO_INCREMENT PRIMARY KEY COMMENT 'id',
    product_big_category_name VARCHAR(255)   NOT NULL COMMENT '产品大类中文名称',
    turnover                  DECIMAL(15, 2) NOT NULL COMMENT '周转天数',
    turnover_yoy              DECIMAL(15, 2) NOT NULL COMMENT '同比',
    turnover_mom              DECIMAL(15, 2) NOT NULL COMMENT '环比',
    update_month              varchar(50)    NOT NULL COMMENT '更新月份yyyy-MM'
) comment '周转天数按产品大类月份统计';

-- 入库金额按月份统计
CREATE TABLE dwd_inventory_amount_in_month
(
    id                        INT AUTO_INCREMENT PRIMARY KEY COMMENT 'id',
    paper_small_category_name VARCHAR(255)   NOT NULL COMMENT '纸张小类中文名称',
    amount_in                 DECIMAL(15, 2) NOT NULL COMMENT '入库金额',
    amount_in_yoy             DECIMAL(15, 2) NOT NULL COMMENT '同比',
    amount_in_mom             DECIMAL(15, 2) NOT NULL COMMENT '环比',
    update_month              varchar(50)    NOT NULL COMMENT '更新月份yyyy-MM'
) comment '入库金额按纸张小类月份统计';

-- 出库金额按月份统计
CREATE TABLE dwd_inventory_amount_out_month
(
    id                        INT AUTO_INCREMENT PRIMARY KEY COMMENT 'id',
    paper_small_category_name VARCHAR(255)   NOT NULL COMMENT '纸张小类中文名称',
    amount_out                DECIMAL(15, 2) NOT NULL COMMENT '出库金额',
    amount_out_yoy            DECIMAL(15, 2) NOT NULL COMMENT '同比',
    amount_out_mom            DECIMAL(15, 2) NOT NULL COMMENT '环比',
    update_month              varchar(50)    NOT NULL COMMENT '更新月份yyyy-MM'
) comment '出库金额按纸张小类月份统计';

-- 纸张小类维度表
CREATE TABLE dim_paper_small_category
(
    id   INT AUTO_INCREMENT PRIMARY KEY COMMENT 'id',
    code VARCHAR(255) NOT NULL COMMENT '产品大类编码',
    name VARCHAR(255) NOT NULL COMMENT '产品大类中文名称'
) comment '纸张小类维度表';

-- 采购费用月份统计
CREATE TABLE dwd_procurement_cost_month
(
    id                     INT AUTO_INCREMENT PRIMARY KEY COMMENT 'id',
    material_category_name VARCHAR(255)   NOT NULL COMMENT '物料类别中文名称',
    procurement_cost       DECIMAL(15, 2) NOT NULL COMMENT '采购金额',
    procurement_cost_yoy   DECIMAL(15, 2) NOT NULL COMMENT '同比',
    procurement_cost_mom   DECIMAL(15, 2) NOT NULL COMMENT '环比',
    update_month           varchar(50)    NOT NULL COMMENT '更新月份yyyy-MM'
) comment '采购费用按物料类别月份统计';

-- 发货达成率
CREATE TABLE dwd_shipment_achievement_rate_month
(
    id                   INT AUTO_INCREMENT PRIMARY KEY COMMENT 'id',
    achievement_rate     DECIMAL(15, 2) NOT NULL COMMENT '达成率',
    achievement_rate_yoy DECIMAL(15, 2) NOT NULL COMMENT '同比',
    achievement_rate_mom DECIMAL(15, 2) NOT NULL COMMENT '环比',
    update_month         varchar(50)    NOT NULL COMMENT '更新月份yyyy-MM'
) comment '发货达成率按月份统计';

-- 日期维度表
CREATE TABLE dim_date
(
    date_id      INT PRIMARY KEY COMMENT '日期ID（例如20240101）',
    full_date    DATE NOT NULL COMMENT '完整日期',
    year         INT  NOT NULL COMMENT '年份',
    month        INT  NOT NULL COMMENT '月份',
    day          INT  NOT NULL COMMENT '日',
    quarter      INT  NOT NULL COMMENT '季度',
    day_of_week  INT  NOT NULL COMMENT '星期几（1=周一，7=周日）',
    week_of_year INT  NOT NULL COMMENT '一年中的第几周'
) comment '日期维度表';

-- 发货总结周统计
CREATE TABLE dwd_shipment_summary_week
(
    id                       INT AUTO_INCREMENT PRIMARY KEY COMMENT 'id',
    planned_shipment_batches INT           NOT NULL COMMENT '计划发货批次',
    actual_shipment_batches  INT           NOT NULL COMMENT '实际发货批次',
    actual_shipment_boxes    INT           NOT NULL COMMENT '实际发货箱数',
    actual_shipment_quantity INT           NOT NULL COMMENT '实际发货数量',
    batch_achievement_rate   DECIMAL(5, 2) NOT NULL COMMENT '批次达成率',
    new_batches              INT           NOT NULL COMMENT '新增批次',
    shipment_batches         INT           NOT NULL COMMENT '发货批次',
    remaining_batches        INT           NOT NULL COMMENT '剩余批次',
    update_year              INT           NOT NULL COMMENT '更新年份',
    week_of_year             INT           NOT NULL COMMENT '一年中的第几周'
) comment '发货总结按周统计';

-- 发货总结月统计
CREATE TABLE dwd_shipment_summary_month
(
    id                       INT AUTO_INCREMENT PRIMARY KEY COMMENT 'id',
    planned_shipment_batches INT           NOT NULL COMMENT '计划发货批次',
    actual_shipment_batches  INT           NOT NULL COMMENT '实际发货批次',
    actual_shipment_boxes    INT           NOT NULL COMMENT '实际发货箱数',
    actual_shipment_quantity INT           NOT NULL COMMENT '实际发货数量',
    batch_achievement_rate   DECIMAL(5, 2) NOT NULL COMMENT '批次达成率',
    new_batches              INT           NOT NULL COMMENT '新增批次',
    shipment_batches         INT           NOT NULL COMMENT '发货批次',
    remaining_batches        INT           NOT NULL COMMENT '剩余批次',
    update_year              INT           NOT NULL COMMENT '更新年份',
    month_of_year            INT           NOT NULL COMMENT '月份'
) comment '发货总结按月统计';

-- 发货明细按周统计
CREATE TABLE dwd_shipment_detail_week
(
    id                       INT AUTO_INCREMENT PRIMARY KEY COMMENT '唯一标识符',
    production_batch_number  VARCHAR(150) NOT NULL COMMENT '生产批次号',
    customer_name            VARCHAR(255) NOT NULL COMMENT '客户名称',
    product_code             VARCHAR(100) NOT NULL COMMENT '产品编码',
    product_name             VARCHAR(255) NOT NULL COMMENT '产品名称',
    order_quantity           INT          NOT NULL COMMENT '订单数量',
    planned_shipment_date    DATE         NOT NULL COMMENT '计划发货日期',
    actual_shipment_date     DATE COMMENT '实际发货日期',
    actual_shipment_quantity INT COMMENT '实际发货数量',
    order_status             VARCHAR(50)  NOT NULL COMMENT '订单状态',
    detail_type              INT          NOT NULL COMMENT '明细类型:1计划发货明细2实际发货明细3未发货明细4新增发货明细5完成新增发货明细6未完成新增发货明细',
    update_year              INT          NOT NULL COMMENT '更新年份',
    week_of_year             INT          NOT NULL COMMENT '一年中的第几周'
) comment '发货明细按周统计';

-- 发货明细按月统计
CREATE TABLE dwd_shipment_detail_month
(
    id                       INT AUTO_INCREMENT PRIMARY KEY COMMENT '唯一标识符',
    production_batch_number  VARCHAR(150) NOT NULL COMMENT '生产批次号',
    customer_name            VARCHAR(255) NOT NULL COMMENT '客户名称',
    product_code             VARCHAR(100) NOT NULL COMMENT '产品编码',
    product_name             VARCHAR(255) NOT NULL COMMENT '产品名称',
    order_quantity           INT          NOT NULL COMMENT '订单数量',
    planned_shipment_date    DATE         NOT NULL COMMENT '计划发货日期',
    actual_shipment_date     DATE COMMENT '实际发货日期',
    actual_shipment_quantity INT COMMENT '实际发货数量',
    order_status             VARCHAR(50)  NOT NULL COMMENT '订单状态',
    detail_type              INT          NOT NULL COMMENT '明细类型:1计划发货明细2实际发货明细3未发货明细4新增发货明细5完成新增发货明细6未完成新增发货明细',
    update_year              INT          NOT NULL COMMENT '更新年份',
    month_of_year            INT          NOT NULL COMMENT '月份'
) comment '发货明细按月统计';

-- 产品库存按周统计
CREATE TABLE dwd_product_inventory_week
(
    id                        INT AUTO_INCREMENT PRIMARY KEY COMMENT '唯一标识符',
    product_big_category_name VARCHAR(255) NOT NULL COMMENT '产品大类中文名称',
    quantity                  INT          NOT NULL COMMENT '数量',
    boxes                     INT          NOT NULL COMMENT '箱数',
    pallets                   INT          NOT NULL COMMENT '托盘数',
    update_year               INT          NOT NULL COMMENT '更新年份',
    week_of_year              INT          NOT NULL COMMENT '一年中的第几周'
) comment '产品库存按周统计';

-- 纸张库存按周统计
CREATE TABLE dwd_product_inventory_week
(
    id                      INT AUTO_INCREMENT PRIMARY KEY COMMENT '唯一标识符',
    paper_big_category_name VARCHAR(255)   NOT NULL COMMENT '纸张大类中文名称',
    tonnage                 DECIMAL(10, 2) NOT NULL COMMENT '吨数',
    pieces                  INT            NOT NULL COMMENT '件数',
    average_weight          DECIMAL(10, 2) NOT NULL COMMENT '均重',
    specification           INT            NOT NULL COMMENT '规格',
    update_year             INT            NOT NULL COMMENT '更新年份',
    week_of_year            INT            NOT NULL COMMENT '一年中的第几周'
) comment '纸张库存按周统计';

-- 纸箱库存按周统计
CREATE TABLE dwd_box_inventory_week
(
    id                INT AUTO_INCREMENT PRIMARY KEY COMMENT '唯一标识符',
    box_sets          INT NOT NULL COMMENT '纸箱套数',
    box_specification INT NOT NULL COMMENT '纸箱规格数',
    pallets           INT NOT NULL COMMENT '纸箱托盘数',
    separator_sheets  INT NOT NULL COMMENT '隔板张数',
    update_year       INT NOT NULL COMMENT '更新年份',
    week_of_year      INT NOT NULL COMMENT '一年中的第几周'
) comment '纸箱库存按周统计';

-- 纸张大类维度表
CREATE TABLE dim_paper_big_category
(
    id   INT AUTO_INCREMENT PRIMARY KEY COMMENT 'id',
    code VARCHAR(255) NOT NULL COMMENT '纸张大类编码',
    name VARCHAR(255) NOT NULL COMMENT '纸张大类中文名称'
) comment '纸张大类维度表';

-- 未发货成品追踪按周统计
CREATE TABLE dwd_unshipped_product_tracking_week
(
    id                        INT AUTO_INCREMENT PRIMARY KEY COMMENT '唯一标识符',
    customer_rating           VARCHAR(50)  NOT NULL COMMENT '客户评级',
    product_big_category_name VARCHAR(255) NOT NULL COMMENT '产品大类',
    entry_type                VARCHAR(50)  NOT NULL COMMENT '入库类别',
    final_status              VARCHAR(50)  NOT NULL COMMENT '最终状态',
    customer_name             VARCHAR(255) NOT NULL COMMENT '客户名称',
    production_batch_number   VARCHAR(100) NOT NULL COMMENT '生产批次号',
    inventory_days            INT          NOT NULL COMMENT '本期库存天数',
    inventory_box_count       INT          NOT NULL COMMENT '库存箱数',
    update_year               INT          NOT NULL COMMENT '更新年份',
    week_of_year              INT          NOT NULL COMMENT '一年中的第几周'
) comment '未发货成品追踪按周统计';


-- 接单量金额按月统计
CREATE TABLE dwd_order_amount_month
(
    id                        INT AUTO_INCREMENT PRIMARY KEY COMMENT 'id',
    product_big_category_name VARCHAR(255)   NOT NULL COMMENT '产品大类中文名称',
    amount                    DECIMAL(15, 2) NOT NULL COMMENT '接单量金额',
    amount_yoy                DECIMAL(15, 2) NOT NULL COMMENT '接单量同比',
    amount_mom                DECIMAL(15, 2) NOT NULL COMMENT '接单量环比',
    update_month              varchar(50)    NOT NULL COMMENT '更新月份yyyy-MM'
) comment '接单量金额按月统计';

-- 待产/已下达订单按月统计
CREATE TABLE dwd_pending_order_month
(
    id                        INT AUTO_INCREMENT PRIMARY KEY COMMENT 'id',
    product_big_category_name VARCHAR(255)   NOT NULL COMMENT '产品大类中文名称',
    order_status              INT            NOT NULL COMMENT '订单类型:1待产2已下达3合计',
    quantity                  INT            NOT NULL COMMENT '数量',
    quantity_yoy              DECIMAL(15, 2) NOT NULL COMMENT '数量同比',
    quantity_mom              DECIMAL(15, 2) NOT NULL COMMENT '数量环比',
    update_month              varchar(50)    NOT NULL COMMENT '更新月份yyyy-MM'
) comment '待产/已下达订单按月统计';

-- 日排产记录
CREATE TABLE arrange_record
(
    id             int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    machine_code   varchar(255) DEFAULT NULL COMMENT '机台编码',
    machine_name   varchar(255) DEFAULT NULL COMMENT '机台名称',
    process_code   varchar(255) DEFAULT NULL COMMENT '工序编码',
    process_name   varchar(255) DEFAULT NULL COMMENT '工序名称',
    work_time      varchar(255) DEFAULT NULL COMMENT '标准工时',
    sample_num     varchar(255) DEFAULT NULL COMMENT '打样排产',
    batch_num      varchar(255) DEFAULT NULL COMMENT '批量排产',
    reality_finish varchar(255) DEFAULT NULL COMMENT '实际完成',
    reality_finish varchar(255) DEFAULT NULL COMMENT '差值',
    day_num        varchar(255) DEFAULT NULL COMMENT '日期',
    PRIMARY KEY (id)
) COMMENT = '日排产记录';


-- 排产自定义报表
CREATE TABLE custom_report
(
    id           int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    process_code varchar(255) DEFAULT NULL COMMENT '工序编码',
    report_code  varchar(255) DEFAULT NULL COMMENT '报告编码',
    report_name  varchar(255) DEFAULT NULL COMMENT '报告名称',
    day_num      varchar(255) DEFAULT NULL COMMENT '天数',
    PRIMARY KEY (id)
) COMMENT = '排产自定义报表';

-- 计划消耗按月统计
CREATE TABLE dwd_planned_consumption_rate_month
(
    id                        INT AUTO_INCREMENT PRIMARY KEY COMMENT 'id',
    product_big_category_name VARCHAR(255)   NOT NULL COMMENT '产品大类中文名称',
    rate                      DECIMAL(15, 2) NOT NULL COMMENT '计划消耗率',
    rate_yoy                  DECIMAL(15, 2) NOT NULL COMMENT '计划消耗率同比',
    rate_mom                  DECIMAL(15, 2) NOT NULL COMMENT '计划消耗率环比',
    update_month              varchar(50)    NOT NULL COMMENT '更新月份yyyy-MM'
) comment '计划消耗按月统计';

-- 待产时间
CREATE TABLE dwd_pending_production_time_month
(
    id                          INT AUTO_INCREMENT PRIMARY KEY COMMENT 'id',
    product_big_category_name   VARCHAR(255)   NOT NULL COMMENT '产品大类中文名称',
    pending_production_time     DECIMAL(15, 2) NOT NULL COMMENT '待产时间',
    pending_production_time_yoy DECIMAL(15, 2) NOT NULL COMMENT '待产时间同比',
    pending_production_time_mom DECIMAL(15, 2) NOT NULL COMMENT '待产时间环比',
    update_month                varchar(50)    NOT NULL COMMENT '更新月份yyyy-MM'
) comment '待产时间按月统计';

-- 运输时间
CREATE TABLE dwd_transport_time_month
(
    id                 INT AUTO_INCREMENT PRIMARY KEY COMMENT 'id',
    transport_time     DECIMAL(15, 2) NOT NULL COMMENT '运输时间',
    transport_time_yoy DECIMAL(15, 2) NOT NULL COMMENT '运输时间同比',
    transport_time_mom DECIMAL(15, 2) NOT NULL COMMENT '运输时间环比',
    update_month       varchar(50)    NOT NULL COMMENT '更新月份yyyy-MM'
) comment '运输时间按月统计';

-- 打样周期
CREATE TABLE dwd_sample_production_cycle_month
(
    id             INT AUTO_INCREMENT PRIMARY KEY COMMENT 'id',
    cycle_time     DECIMAL(15, 2) NOT NULL COMMENT '打样周期时间',
    cycle_time_yoy DECIMAL(15, 2) NOT NULL COMMENT '运输时间同比',
    cycle_time_mom DECIMAL(15, 2) NOT NULL COMMENT '运输时间环比',
    update_month   varchar(50)    NOT NULL COMMENT '更新月份yyyy-MM'
) comment '打样周期按月统计';

-- 时间节点统计
CREATE TABLE dwd_time_node_mom
(
    id           int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    update_date  date           DEFAULT NULL COMMENT '日期yyyy-MM-dd',
    function     varchar(255)   DEFAULT NULL COMMENT '功能',
    node         varchar(255)   DEFAULT NULL COMMENT '节点',
    unfinished   int(11)        DEFAULT NULL COMMENT '未完成数量',
    finished     int(11)        DEFAULT NULL COMMENT '已完成数量',
    finished_avg decimal(10, 2) DEFAULT NULL COMMENT '已完成平均耗时',
    PRIMARY KEY (id)
) COMMENT = '时间节点统计mom';

-- 时间节点统计OA
CREATE TABLE dwd_time_node_oa
(
    id           int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    audit        varchar(255)   DEFAULT NULL COMMENT '审批流',
    unfinished   int(11)        DEFAULT NULL COMMENT '未完成数量',
    finished     int(11)        DEFAULT NULL COMMENT '已完成数量',
    finished_avg decimal(10, 2) DEFAULT NULL COMMENT '已完成平均耗时',
    PRIMARY KEY (id)
) COMMENT = '时间节点统计oa';

-- 内容：审批流明细
CREATE TABLE ods_audit_detail
(
    id           int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    audit        varchar(255) DEFAULT NULL COMMENT '审批流',
    applicant    varchar(255) DEFAULT NULL COMMENT '申请人',
    apply_dept   varchar(255) DEFAULT NULL COMMENT '申请部门',
    apply_time   varchar(255) DEFAULT NULL COMMENT '申请时间',
    current_node varchar(255) DEFAULT NULL COMMENT '当前节点',
    audit_time   varchar(255) DEFAULT NULL COMMENT '累计审批时间',
    detail       varchar(255) DEFAULT NULL COMMENT '详情',
    PRIMARY KEY (id)
) COMMENT = '审批流明细';

CREATE TABLE ods_audit_detail
(
    id           int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    audit        varchar(255) DEFAULT NULL COMMENT '审批流',
    applicant    varchar(255) DEFAULT NULL COMMENT '申请人',
    apply_dept   varchar(255) DEFAULT NULL COMMENT '申请部门',
    apply_time   varchar(255) DEFAULT NULL COMMENT '申请时间',
    current_node varchar(255) DEFAULT NULL COMMENT '当前节点',
    audit_time   varchar(255) DEFAULT NULL COMMENT '累计审批时间',
    detail       varchar(255) DEFAULT NULL COMMENT '详情',
    PRIMARY KEY (id)
) COMMENT = '审批流明细';


-- 工序废品率占比
CREATE TABLE dwd_process_scrap_rate_month
(
    id           int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    update_month varchar(255)  DEFAULT NULL COMMENT '更新月份yyyy-MM',
    process_code varchar(255)  DEFAULT NULL COMMENT '工序',
    process_name varchar(255)  DEFAULT NULL COMMENT '工序名称',
    scrap_rate   decimal(5, 2) DEFAULT NULL COMMENT '工序废品率',
    PRIMARY KEY (id)
) COMMENT = '工序废品率占比';

-- 废品类型占比
CREATE TABLE dwd_scrap_rate_month
(
    id           int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    update_month varchar(255)  DEFAULT NULL COMMENT '更新月份yyyy-MM',
    scrap_type   varchar(255)  DEFAULT NULL COMMENT '废品类型',
    scrap_rate   decimal(5, 2) DEFAULT NULL COMMENT '废品类型占比',
    PRIMARY KEY (id)
) COMMENT = '废品类型占比';


-- 过程检验统计
CREATE TABLE dwd_course_inspect_month
(
    id                      int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    update_month            varchar(255)   DEFAULT NULL COMMENT '更新月份yyyy-MM',
    process_code            varchar(255)   DEFAULT NULL COMMENT '工序代码',
    process_name            varchar(255)   DEFAULT NULL COMMENT '工序名称',
    monthly_first_inspect   int(11)        DEFAULT NULL COMMENT '月度首检次数',
    first_inspect_fail      int(11)        DEFAULT NULL COMMENT '首检不合格批次',
    first_inspect_deviation int(11)        DEFAULT NULL COMMENT '首检偏差放行批次',
    first_inspect_pass_rate decimal(5, 2)  DEFAULT NULL COMMENT '首检一次交验合格率',
    avg_first_inspect       decimal(10, 2) DEFAULT NULL COMMENT '人均首检批次',
    total_patrol_inspect    int(11)        DEFAULT NULL COMMENT '月度总巡检频次',
    process_fail            int(11)        DEFAULT NULL COMMENT '制程不合格发现批次',
    process_deviation       int(11)        DEFAULT NULL COMMENT '制程不合格偏差放行批次',
    patrol_pass_rate        decimal(5, 2)  DEFAULT NULL COMMENT '巡检合格率',
    avg_patrol_inspect      decimal(10, 2) DEFAULT NULL COMMENT '人均巡检频次',
    PRIMARY KEY (id)
) COMMENT = '过程检验统计';

-- 作业色序统计
CREATE TABLE dwd_job_color_sequence_month
(
    id                       INT AUTO_INCREMENT PRIMARY KEY COMMENT 'id',
    production_batch_number  VARCHAR(50)  NOT NULL COMMENT '生产批次号',
    product_code             VARCHAR(50)  NOT NULL COMMENT '产品编码',
    product_name             VARCHAR(100) NOT NULL COMMENT '产品名称',
    issue_date               DATE         NOT NULL COMMENT '下达日期',
    printing_completion_date DATE         NOT NULL COMMENT '印刷完工日期',
    color_count              INT          NOT NULL COMMENT '颜色数量',
    special_color_count      INT          NOT NULL COMMENT '专色数量',
    color_sequence           VARCHAR(50)  NOT NULL COMMENT '色序',
    update_month             varchar(50)  NOT NULL COMMENT '更新月份yyyy-MM'
) comment '作业色序按月统计';
