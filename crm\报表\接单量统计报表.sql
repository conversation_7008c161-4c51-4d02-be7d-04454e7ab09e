-- 接单量统计报表
-- 销售合同 合同状态status: 0-待下发、1-部分下发、2-已下发、3-已作废、4-已关闭
-- 拆分订单 订单状态status：0-已拆分,1-已下达,3-已领料,4-生产中,5-已入库,6-已开票,7-已关闭,8-已取消,11-已发货未开票
select '西安环球' as                                                                                        sale_company,
       t3.deparment_code,
       t3.deparment_name,
       t3.department_region                                                                                 region,
       t3.sales_assistant_code,
       t3.sales_assistant_name,
       t3.cust_manager_code,
       t3.cust_manager_name,
       t3.cust_vip,
       t3.cust_code,
       t3.cust_name,
       t1.contract_management_code,
       t1.quotation_code,
       STR_TO_DATE(t1.documentation_date, '%Y-%m-%d')                                                       documentation_date,
       t2.main_class,
       t2.sub_class,
       t2.material_code,
       t2.material_name,
       t2.product_version,
       t2.product_quantity,
       round(IF(t2.unit_price NOT REGEXP '^-?[0-9]+(\\.[0-9]+)?$', 0, t2.unit_price ), 4) unit_price,
       round(IF(t2.unit_price_exclusive NOT REGEXP '^-?[0-9]+(\\.[0-9]+)?$', 0, t2.unit_price_exclusive ),
             4)                                                                                             unit_price_exclusive,
       case
           when t1.status in (0, 1, 2) then
               round(IF(t2.amount_tax_inclusive NOT REGEXP '^-?[0-9]+(\\.[0-9]+)?$', 0,
                        t2.amount_tax_inclusive ),
                     2)
           when t1.status = 3 then 0
           when t1.status = 4 then t4.amount_tax_inclusive
           end
                                                                                                            amount_tax_inclusive,
       case
           when t1.status in (0, 1, 2) then
               round(IF(t2.amount_exclusive_tax NOT REGEXP '^-?[0-9]+(\\.[0-9]+)?$', 0,
                        t2.amount_exclusive_tax ),
                     2)
           when t1.status = 3 then 0
           when t1.status = 4 then t4.amount_exclusive_tax
           end
                                                                                                            amount_exclusive_tax,
       t2.settlement_currency,
       round(IF(t2.settlement_currency_price_exclusive NOT REGEXP '^-?[0-9]+(\\.[0-9]+)?$', 0,
                t2.settlement_currency_price_exclusive ),
             4)                                                                                             settlement_currency_price_exclusive,
       case
           when t1.status in (0, 1, 2) then
               round(IF(t2.settlement_currency_amount_exclusive NOT REGEXP '^-?[0-9]+(\\.[0-9]+)?$', 0,
                        t2.settlement_currency_amount_exclusive ), 2)
           when t1.status = 3 then 0
           when t1.status = 4 then t4.settlement_currency_amount_exclusive
           end                                                                                          settlement_currency_amount_exclusive,
       t1.status,
       case t1.status
           when '0' then '待下发'
           when '1' then '部分下发'
           when '2' then '已下发'
           when '3' then '已作废'
           when '4' then '已关闭'
           else '其他' end status_desc,
       t2.delivery_date,
       t2.object_id
from crm_contract_management t1
         left join crm_contract_management_product t2 on t2.contract_management_code = t1.contract_management_code
         left join crm_cust_basic t3 on t3.cust_code = t1.cust_code
         left join (select t1.contract_management_code,
                           t2.material_code,
                           round(sum(IF(t2.amount_tax_inclusive NOT REGEXP '^-?[0-9]+(\\.[0-9]+)?$', 0,
                                        t2.amount_tax_inclusive )),
                                 2) amount_tax_inclusive,
                           round(sum(IF(t2.amount_exclusive_tax NOT REGEXP '^-?[0-9]+(\\.[0-9]+)?$', 0,
                                        t2.amount_exclusive_tax )),
                                 2) amount_exclusive_tax,
                           round(sum(IF(t2.settlement_currency_amount_exclusive NOT REGEXP '^-?[0-9]+(\\.[0-9]+)?$', 0,
                                        t2.settlement_currency_amount_exclusive )),
                                 2) settlement_currency_amount_exclusive
                    FROM crm_sales_order t1
                             LEFT JOIN crm_sales_order_product t2 ON
                        t1.sales_order_code = t2.sales_order_code
                    where status != 8
                      and t1.flag_deleted = 0
                      and t2.flag_deleted = 0
                    group by t1.contract_management_code,t2.material_code) t4
                   on t1.contract_management_code = t4.contract_management_code and t2.material_code=t4.material_code
where t1.flag_deleted = 0
  and t2.flag_deleted = 0
  and t3.flag_deleted = 0
  and t3.cust_status=2 and t3.cust_type not in (0,1)
#   AND if(:admin,
#          1,
#          if(:cust_code_size>0, t3.cust_code in (:cust_code_arr), 1)
#       )
#   AND ((:deparment_code IS NULL OR :deparment_code = '') OR (t3.deparment_code = :deparment_code))
#   AND ((:region IS NULL OR :region = '') OR (t3.department_region = :region))
#   AND ((:cust_code IS NULL OR :cust_code = '') OR (t3.cust_code like concat('%', :cust_code, '%')))
#   AND ((:cust_name IS NULL OR :cust_name = '') OR (t3.cust_name like concat('%', :cust_name, '%')))
#   AND ((:material_code IS NULL OR :material_code = '') OR (t2.material_code like concat('%', :material_code, '%')))
#   AND ((:material_name IS NULL OR :material_name = '') OR (t2.material_name like concat('%', :material_name, '%')))
#   AND ((:status IS NULL OR :status = '') OR (status =:status))
#   AND ((:contract_management_code IS NULL OR :contract_management_code = '') OR
#        (t1.contract_management_code like concat('%', :contract_management_code, '%')))
#   AND ((:contract_start_date IS NULL OR :contract_start_date = '') OR
#        (date_format(documentation_date, '%Y-%m-%d') >= :contract_start_date))
#   AND ((:contract_end_date IS NULL OR :contract_end_date = '') OR
#        (date_format(documentation_date, '%Y-%m-%d') <= :contract_end_date))
#   AND if(:cust_manager_size>0, t3.cust_manager_code in (:cust_manager_arr), 1)
#   AND ((:main_class IS NULL OR :main_class = '') OR (t2.main_class =:main_class))
#   AND if(:sales_assistant_size>0,t3.sales_assistant_code REGEXP :sales_assistant_str,1)
# order by t1.documentation_date desc
# limit :page_size offset :offset
;

select main_class from crm_sales_order_product csop where main_class is not null group by main_class;


select '合计' as                                                sale_company,
       round(sum(temp.product_quantity), 2)                     product_quantity,
       round(sum(temp.amount_tax_inclusive), 2)                 amount_tax_inclusive,
       round(sum(temp.amount_exclusive_tax), 2)                 amount_exclusive_tax,
       round(sum(temp.settlement_currency_price_exclusive), 4)  settlement_currency_price_exclusive,
       round(sum(temp.settlement_currency_amount_exclusive), 2) settlement_currency_amount_exclusive
from (select
             t2.product_quantity,
             case
                 when t1.status in (0, 1, 2) then
                     round(IF(t2.amount_tax_inclusive NOT REGEXP '^-?[0-9]+(\\.[0-9]+)?$', 0,
                              t2.amount_tax_inclusive ),
                           2)
                 when t1.status = 3 then 0
                 when t1.status = 4 then t4.amount_tax_inclusive
                 end
                                                                                                                  amount_tax_inclusive,
             case
                 when t1.status in (0, 1, 2) then
                     round(IF(t2.amount_exclusive_tax NOT REGEXP '^-?[0-9]+(\\.[0-9]+)?$', 0,
                              t2.amount_exclusive_tax ),
                           2)
                 when t1.status = 3 then 0
                 when t1.status = 4 then t4.amount_exclusive_tax
                 end
                                                                                                                  amount_exclusive_tax,
             round(IF(t2.settlement_currency_price_exclusive NOT REGEXP '^-?[0-9]+(\\.[0-9]+)?$', 0,
                      t2.settlement_currency_price_exclusive ),
                   4)                                                                                             settlement_currency_price_exclusive,
             case
                 when t1.status in (0, 1, 2) then
                     round(IF(t2.settlement_currency_amount_exclusive NOT REGEXP '^-?[0-9]+(\\.[0-9]+)?$', 0,
                              t2.settlement_currency_amount_exclusive ), 2)
                 when t1.status = 3 then 0
                 when t1.status = 4 then t4.settlement_currency_amount_exclusive
                 end                                                                                          settlement_currency_amount_exclusive,
             t1.status

      from crm_contract_management t1
               left join crm_contract_management_product t2 on t2.contract_management_code = t1.contract_management_code
               left join crm_cust_basic t3 on t3.cust_code = t1.cust_code
               left join (select t1.contract_management_code,
                                 round(sum(IF(t2.amount_tax_inclusive NOT REGEXP '^-?[0-9]+(\\.[0-9]+)?$', 0,
                                              t2.amount_tax_inclusive )),
                                       2) amount_tax_inclusive,
                                 round(sum(IF(t2.amount_exclusive_tax NOT REGEXP '^-?[0-9]+(\\.[0-9]+)?$', 0,
                                              t2.amount_exclusive_tax )),
                                       2) amount_exclusive_tax,
                                 round(sum(IF(t2.settlement_currency_amount_exclusive NOT REGEXP '^-?[0-9]+(\\.[0-9]+)?$', 0,
                                              t2.settlement_currency_amount_exclusive )),
                                       2) settlement_currency_amount_exclusive
                          FROM crm_sales_order t1
                                   LEFT JOIN crm_sales_order_product t2 ON
                              t1.sales_order_code = t2.sales_order_code
                          where status != 8
                            and t1.flag_deleted = 0
                            and t2.flag_deleted = 0
                          group by t1.contract_management_code) t4
                         on t1.contract_management_code = t4.contract_management_code
      where t1.flag_deleted = 0
        and t2.flag_deleted = 0
        and t3.flag_deleted = 0
        and t3.cust_status=2 and t3.cust_type not in (0,1)
        and year(documentation_date)=year(now())
      AND if(:admin,
             1,
             if(:cust_code_size>0, t3.cust_code in (:cust_code_arr), 1)
          )
      order by t1.id asc) temp;



