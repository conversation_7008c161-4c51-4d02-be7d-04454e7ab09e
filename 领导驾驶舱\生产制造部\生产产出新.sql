-- 折叠纸盒生产批次
SELECT  count(*) as can_1,sum(CASE WHEN received_quantity is not null and received_quantity!="" THEN received_quantity ELSE 0 END )as can_2, DATE_FORMAT(finish_date, '%Y-%m') AS yuefen
FROM ods_pm_job_order
WHERE production_order_status="3" and YEAR(finish_date) =:nian and large_category=:id
GROUP BY yuefen;
SELECT  count(*) as can_5,sum(CASE WHEN received_quantity is not null and received_quantity!="" THEN received_quantity ELSE 0 END )as can_8, DATE_FORMAT(finish_date, '%Y-%m') AS yuefen
FROM ods_pm_job_order
WHERE production_order_status="3" and YEAR(finish_date) =:nian and large_category=:id
GROUP BY yuefen;
SELECT  count(*) as can_4,sum(CASE WHEN return_product_quantity is not null and return_product_quantity!="" THEN return_product_quantity ELSE 0 END )as can_7,DATE_FORMAT(update_time, '%Y-%m') AS yuefen
FROM ods_pm_job_order_outside_apply
WHERE production_batch_number in (:production_batch_number)
  and  outside_types="1"
  and  outside_status=4
  and YEAR(update_time) =:nian
GROUP BY yuefen;

-- 折叠纸盒, 废品率
SELECT  round(sum(quantity_rejects )) as can_12,
        DATE_FORMAT(finish_date, '%Y-%m') AS yuefen
FROM pm_job_order
WHERE production_order_status='3' and YEAR(finish_date) =2025 and large_category=6876
and flag_deleted=0
and quantity_rejects<pm_job_order.feed_quantity
GROUP BY yuefen
;
-- 投料
select round(sum(feed_quantity_big)) as can_12 ,
       DATE_FORMAT(create_time, '%Y-%m') AS yuefen    from
    (SELECT distinct  t1.id,t1.feed_quantity_big,t1.create_time
     FROM pm_final_batch_job_audit t1  JOIN
          pm_job_detail t2 ON t1.job_strip_number = t2.id
                                       join pm_job_order t3 on t2.production_batch_number=t3.production_batch_number
     WHERE t1.flag_deleted = 0 and t2.flag_deleted=0 and t3.flag_deleted=0
       and year(t1.create_time) = year(curdate()) and
         t3. production_order_status='3' and YEAR(finish_date) =2025 and large_category=6876
    )temp group by yuefen;
-- 废品量
select round(sum(scrap_volume)) as can_12 ,
    DATE_FORMAT(create_time, '%Y-%m') AS yuefen    from
(SELECT distinct  t1.id,t1.scrap_volume,t1.create_time
FROM pm_final_batch_job_audit t1  JOIN
     pm_job_detail t2 ON t1.job_strip_number = t2.id
 join pm_job_order t3 on t2.production_batch_number=t3.production_batch_number
WHERE t1.flag_deleted = 0 and t2.flag_deleted=0 and t3.flag_deleted=0
  and year(t1.create_time) = year(curdate()) and
t3. production_order_status='3' and YEAR(finish_date) =2025 and large_category=6876
    )temp group by yuefen
;



