select * from crm_shipping_info csi where shipment_code='XSFH250624000013';
select * from crm_shipment_plan csp where shipment_code='XSFH250624000013';

SELECT
    cso.csaleorderid,
    cso.factory_assigned,
    csp.shipment_code AS shipments_code,
    '1' AS shipments_type,
    csp.create_time AS production_date,
    csp.creator AS shipments_planner,
    '' AS shipments_department,
    csp.remark AS remark,
    '0' AS status_code,
    ccb.cust_code AS external_cust,
    cso.sales_order_code
FROM
    crm_sales_order cso
        LEFT JOIN crm_cust_basic ccb ON
        ccb.cust_code = cso.cust_code
            AND ccb.flag_deleted = 0
            AND ccb.cust_type not in ('0','1')
            -- AND ccb.cust_status =2
        LEFT JOIN crm_shipment_plan csp ON
        csp.sales_order_code = cso.sales_order_code
WHERE
    cso.flag_deleted = 0
  AND csp.flag_deleted = 0
  AND csp.shipment_code = 'XSFH250625000044';

-- 拆分订单发货查询
WITH delivered_data AS (
    SELECT
        csp.sales_order_code,
        csi.material_code,
        SUM(csi.current_shipment_quantity) AS shipped_quantity
    FROM
        crm_shipping_info csi
            LEFT JOIN crm_shipment_plan csp ON
            csi.shipment_code = csp.shipment_code
    WHERE
        csi.flag_deleted = 0
      AND csp.flag_deleted = 0
      AND csp.shipment_status = '4'
      AND csp.sales_order_code = :sales_order_code
    GROUP BY
        csp.sales_order_code,
        csi.material_code
),
     notification_date AS (
         SELECT
             csi.contract_product_line_number,
             csp.sales_order_code,
             csi.material_code,
             SUM(csi.current_shipment_quantity) AS notified_shipment_quantity
         FROM
             crm_shipping_info csi
                 LEFT JOIN crm_shipment_plan csp ON
                 csi.shipment_code = csp.shipment_code
         WHERE
             csi.flag_deleted = 0
           AND csp.flag_deleted = 0
           AND csp.shipment_status != '4'
           AND csp.shipment_status != '3'
           AND csp.sales_order_code = :sales_order_code
         GROUP BY
             csp.sales_order_code,
             csi.material_code,
             csi.contract_product_line_number
     )
SELECT
    csop.id,
    csop.material_code,
    csop.mnemonic_code,
    csop.material_name,
    csop.order_quantity_after_split,
    csop.contract_product_line_number,
    csop.total_order_quantity,
    csop.quantity_fluctuation,
    csop.delivered_quantity,
    IFNULL(csop.delivered_quantity, 0) AS shipped_quantity,
    IFNULL(nd.notified_shipment_quantity, 0) AS notified_shipment_quantity,
    CASE
        WHEN csop.stocked_product IS NULL OR csop.stocked_product = '' THEN '否'
        ELSE csop.stocked_product
        END AS stocked_product,
    csop.standard_unit,
    csop.product_version,
    (select cust_code from crm_sales_order cso where cso.flag_deleted=0 and cso.sales_order_code=csop.sales_order_code limit 1) cust_code
FROM
    crm_sales_order_product csop
        LEFT JOIN delivered_data dd ON
        dd.sales_order_code = csop.sales_order_code
            AND dd.material_code = csop.material_code
        LEFT JOIN notification_date nd ON
        nd.sales_order_code = csop.sales_order_code
            AND nd.material_code = csop.material_code
            and if(nd.contract_product_line_number is not null,nd.contract_product_line_number=csop.contract_product_line_number,1)
WHERE
    csop.flag_deleted = 0
  AND csop.sales_order_code = :sales_order_code;


SELECT
    cso.csaleorderid,
    cso.factory_assigned,
    csp.shipment_code AS shipments_code,
    '1' AS shipments_type,
    csp.create_time AS production_date,
    csp.creator AS shipments_planner,
    '' AS shipments_department,
    csp.remark AS remark,
    '0' AS status_code,
    ccb.cust_code AS external_cust
FROM
    crm_sales_order cso
        LEFT JOIN crm_cust_basic ccb ON
        ccb.cust_code = cso.cust_code
            AND ccb.flag_deleted = 0
            AND ccb.cust_type not in ('0','1')
            AND ccb.cust_status in ('2','4')
        LEFT JOIN crm_shipment_plan csp ON
        csp.sales_order_code = cso.sales_order_code
WHERE
    cso.flag_deleted = 0
  AND csp.flag_deleted = 0
  AND csp.shipment_code = 'XSFH250617000038';


SELECT
    cso.csaleorderid,
    cso.factory_assigned,
    csp.shipment_code AS shipments_code,
    '1' AS shipments_type,
    csp.create_time AS production_date,
    csp.creator AS shipments_planner,
    '' AS shipments_department,
    csp.remark AS remark,
    '0' AS status_code,
    ccb.cust_code AS external_cust
FROM
    crm_sales_order cso
        LEFT JOIN crm_cust_basic ccb ON
        ccb.cust_code = cso.cust_code
            AND ccb.flag_deleted = 0
            AND ccb.cust_type not in ('0','1')
            AND ccb.cust_status in ('2','4')
        LEFT JOIN (
        select p.shipment_code,
               p.create_time,
               p.creator,
               p.remark,
               a.contract_product_line_number,
               pro.sales_order_code
        from crm_shipping_info a
                 left join crm_shipment_plan p on
            a.shipment_code = p.shipment_code
                 left join crm_sales_order_product pro
                           on p.sales_order_code = pro.sales_order_code
                               and a.contract_product_line_number = pro.contract_product_line_number
    ) csp ON
        csp.sales_order_code = cso.sales_order_code
WHERE
    cso.flag_deleted = 0
  AND csp.flag_deleted = 0
  AND csp.shipment_code = 'XSFH250617000038';

select p.shipment_code,
       p.create_time,
       p.creator,
       p.remark,
       a.contract_product_line_number
from crm_shipping_info a
left join crm_shipment_plan p on
a.shipment_code = p.shipment_code
left join crm_sales_order_product pro
on p.sales_order_code = pro.sales_order_code
and a.contract_product_line_number = pro.contract_product_line_number
where  a.shipment_code='XSFH250624000013';



SELECT
    csop.csaleorderbid,
    csp.shipment_code,
    cso.csaleorderid AS order_code,
    csi.contract_product_line_number AS row_no,
    csi.current_shipment_quantity AS main_quantity,
    csi.material_code AS product_number,
    csi.material_name AS product_name,
    csi.float_rate as rate,
    CAST(COALESCE(SUBSTRING_INDEX(csop.product_version, '.', 1), '') AS CHAR) AS product_version,
    cso.cust_name AS cust_name,
    cso.cust_code AS cust_code,
    '1' AS status,
    csp.arrival_date AS delivery_date,
    csp.shipment_date AS plan_delivery_date,
    csp.contact_phone AS receiving_phone,
    csp.contact_name AS consignee,
    csp.shipping_address AS delivery_address,
    csp.administrative_code AS region_code,
    '0' AS is_finished_product,
    csp.remark AS remark
FROM
    crm_sales_order cso
        LEFT JOIN crm_sales_order_product csop ON
        csop.sales_order_code = cso.sales_order_code
        LEFT JOIN crm_cust_basic ccb ON
        ccb.cust_code = cso.cust_code
            AND ccb.flag_deleted = 0
            AND ccb.cust_version = '1'
            AND ccb.cust_type != '0'
            AND ccb.cust_status NOT IN ('0', '3')
            AND (leader_code IS NULL OR leader_code = '')
        LEFT JOIN crm_shipment_plan csp ON
        csp.sales_order_code = cso.sales_order_code
        LEFT JOIN crm_shipping_info csi ON
        csi.shipment_code = csp.shipment_code
            AND csi.material_code = csop.material_code
            and csi.contract_product_line_number=csop.contract_product_line_number
WHERE
    cso.flag_deleted = 0
  AND csop.flag_deleted = 0
  AND csp.flag_deleted = 0
  AND csi.flag_deleted = 0
  AND csp.shipment_code = 'XSFH250617000038';
