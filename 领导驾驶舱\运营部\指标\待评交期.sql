-- ETL:运营部-​待评交期->dwd_yunying_shuomingshu
-- 历史(已废弃)
SELECT 9                                                                                          id,
       COALESCE(ROUND(SUM(COALESCE(b.total, 0) * COALESCE(b.unit_price_no, 0)) / 10000, 0), 0) AS value,
       DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s')                                                    time,
       '待评交期'                                                                                 name
FROM ods_pm_job_order a
         LEFT JOIN ods_pm_order_product b ON a.order_code = b.sales_number AND a.material_code = b.product_number
         left join ods_pm_sale_order c on c.id = b.sales_order_id
WHERE c.status in (1, 2)
  and a.source_type = 1;


-- 最新修改
-- 待评交期:销售订单为成品且状态为 待准备资料、待开作业单的总金额 (销售订单表数据)
SELECT 9                                                                                          id,
       COALESCE(ROUND(SUM(COALESCE(b.total, 0) * COALESCE(b.unit_price_no, 0)) / 10000, 0), 0) AS value,
       DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s')                                                    time,
       '待评交期'                                                                                 name
FROM pm_order_product b
         left join pm_sale_order c on c.id = b.sales_order_id
WHERE c.status in (1, 2)
  and c.flag_deleted = 0
  and b.flag_deleted = 0
  and b.is_finished_product = 0
;


SELECT 9                                       id,
       JSON_ARRAYAGG(
               JSON_OBJECT(
                       'value', value,
                        'quantity',quantity,
                       'major_categories', major_categories
               )
       ) AS                                    value,
       DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s') time,
       '待评交期'                              name
FROM (SELECT IFNULL(ROUND(SUM(COALESCE(b.total, 0) * IFNULL(b.unit_price_no, 0)) / 10000, 0), 0) AS value,
             round(SUM(COALESCE(b.total, 0))/10000,0) as quantity,
             major_categories
      FROM pm_order_product b
               LEFT JOIN pm_sale_order c ON c.id = b.sales_order_id
      WHERE c.status IN (1, 2)
        AND c.flag_deleted = 0
        AND b.flag_deleted = 0
        AND b.is_finished_product = 0
        AND major_categories IS NOT NULL
      GROUP BY major_categories) AS subquery;
