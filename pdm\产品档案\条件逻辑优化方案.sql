-- ========================================
-- 条件逻辑优化方案 (CASE/IF 优化法)
-- ========================================
-- 核心思想：避免不必要的JOIN和子查询，只在参数有值时才执行相关逻辑

-- ========================================
-- 原始查询的问题分析
-- ========================================
-- 1. 即使参数为空，也会执行所有的LEFT JOIN
-- 2. 多个LEFT JOIN导致笛卡尔积
-- 3. 大量的OR条件判断影响索引使用效率

-- ========================================
-- 优化方案1: 基础条件逻辑优化
-- ========================================
SELECT 
    pv.id,
    pv.material_code,
    pv.mnemonic_code,
    pv.material_name,
    pv.is_group_product,
    pv.bom_type,
    pv.main_class_cn,
    pv.sub_class_cn,
    pv.product_version,
    pv.status,
    pv.product_size,
    pv.create_by,
    pv.create_time,
    pv.update_by,
    pv.update_time
FROM pdm_product_version pv
WHERE pv.flag_deleted = 0 
  AND pv.status IN (1,2,3,4)
  -- 文件条件：只在fileFlag='1'时才检查文件存在性
  AND CASE 
    WHEN :fileFlag = '1' THEN 
      EXISTS (
        SELECT 1 FROM pdm_upload_file pf 
        WHERE pf.material_code = pv.material_code 
          AND pf.product_version = pv.product_version 
          AND pf.file_id IS NOT NULL
      )
    ELSE TRUE 
  END
ORDER BY pv.id DESC
LIMIT 10;

-- ========================================
-- 优化方案2: 完整的条件逻辑优化
-- ========================================
SELECT 
    pv.id,
    pv.material_code,
    pv.mnemonic_code,
    pv.material_name,
    pv.is_group_product,
    pv.bom_type,
    pv.main_class_cn,
    pv.sub_class_cn,
    pv.product_version,
    pv.status,
    pv.product_size,
    pv.create_by,
    pv.create_time,
    pv.update_by,
    pv.update_time
FROM pdm_product_version pv
WHERE pv.flag_deleted = 0 
  AND pv.status IN (1,2,3,4)
  
  -- 主表字段条件（直接过滤，效率最高）
  AND (:material_code IS NULL OR :material_code = '' OR pv.material_code LIKE CONCAT('%', :material_code, '%'))
  AND (:material_name IS NULL OR :material_name = '' OR pv.material_name LIKE CONCAT('%', :material_name, '%'))
  AND (:mnemonic_code IS NULL OR :mnemonic_code = '' OR pv.mnemonic_code LIKE CONCAT('%', :mnemonic_code, '%'))
  AND (:is_group IS NULL OR :is_group = '' OR pv.is_group_product = :is_group)
  AND (:bom_type IS NULL OR :bom_type = '' OR pv.bom_type = :bom_type)
  AND (:product_version IS NULL OR :product_version = '' OR pv.product_version = :product_version)
  AND (:product_size IS NULL OR :product_size = '' OR pv.product_size LIKE CONCAT('%', :product_size, '%'))
  
  -- 时间范围条件
  AND (:startcreatetime IS NULL OR :startcreatetime = '' OR pv.create_time >= :startcreatetime)
  AND (:endcreatetime IS NULL OR :endcreatetime = '' OR pv.create_time <= :endcreatetime)
  AND (:startupdatetime IS NULL OR :startupdatetime = '' OR pv.update_time >= :startupdatetime)
  AND (:endupdatetime IS NULL OR :endupdatetime = '' OR pv.update_time <= :endupdatetime)
  
  -- 分类条件（使用IF函数优化）
  AND IF(:main_class_size > 0, pv.main_class IN (:main_class), TRUE)
  AND IF(:sub_class_size > 0, pv.sub_class IN (:sub_class), TRUE)
  
  -- 关联表条件：只在参数有值时才执行EXISTS子查询
  -- 客户端条件
  AND CASE 
    WHEN :client_name IS NULL OR :client_name = '' THEN TRUE
    ELSE EXISTS (
      SELECT 1 FROM pdm_product_client pc 
      WHERE pc.product_code = pv.material_code 
        AND pc.client_name LIKE CONCAT('%', :client_name, '%')
    )
  END
  
  -- BOM相关条件
  AND CASE 
    WHEN :mater_code IS NULL OR :mater_code = '' THEN TRUE
    ELSE EXISTS (
      SELECT 1 FROM pdm_product_bom pb 
      WHERE pb.product_code = pv.material_code 
        AND pb.product_version = pv.product_version
        AND pb.material_code LIKE CONCAT('%', :mater_code, '%')
    )
  END
  AND CASE 
    WHEN :mater_name IS NULL OR :mater_name = '' THEN TRUE
    ELSE EXISTS (
      SELECT 1 FROM pdm_product_bom pb 
      WHERE pb.product_code = pv.material_code 
        AND pb.product_version = pv.product_version
        AND pb.material_name LIKE CONCAT('%', :mater_name, '%')
    )
  END
  AND CASE 
    WHEN :paper_size IS NULL OR :paper_size = '' THEN TRUE
    ELSE EXISTS (
      SELECT 1 FROM pdm_product_bom pb 
      WHERE pb.product_code = pv.material_code 
        AND pb.product_version = pv.product_version
        AND pb.component_size LIKE CONCAT('%', :paper_size, '%')
        AND pb.categroy = '1'
    )
  END
  
  -- 工艺条件
  AND CASE 
    WHEN :processname IS NULL OR :processname = '' THEN TRUE
    ELSE EXISTS (
      SELECT 1 FROM pdm_product_craft ppc 
      WHERE ppc.product_code = pv.material_code 
        AND ppc.product_version = pv.product_version
        AND ppc.craft_name LIKE CONCAT('%', :processname, '%')
    )
  END
  
  -- 文件条件
  AND CASE 
    WHEN :fileFlag = '1' THEN 
      EXISTS (
        SELECT 1 FROM pdm_upload_file pf 
        WHERE pf.material_code = pv.material_code 
          AND pf.product_version = pv.product_version 
          AND pf.file_id IS NOT NULL
      )
    ELSE TRUE 
  END
ORDER BY pv.id DESC
LIMIT 10;

-- ========================================
-- 优化方案3: 使用MySQL的IF函数进一步优化
-- ========================================
SELECT 
    pv.id,
    pv.material_code,
    pv.mnemonic_code,
    pv.material_name,
    pv.is_group_product,
    pv.bom_type,
    pv.main_class_cn,
    pv.sub_class_cn,
    pv.product_version,
    pv.status,
    pv.product_size,
    pv.create_by,
    pv.create_time,
    pv.update_by,
    pv.update_time
FROM pdm_product_version pv
WHERE pv.flag_deleted = 0 
  AND pv.status IN (1,2,3,4)
  
  -- 使用IF函数，语法更简洁
  AND IF(:material_code IS NULL OR :material_code = '', 1, pv.material_code LIKE CONCAT('%', :material_code, '%'))
  AND IF(:material_name IS NULL OR :material_name = '', 1, pv.material_name LIKE CONCAT('%', :material_name, '%'))
  AND IF(:mnemonic_code IS NULL OR :mnemonic_code = '', 1, pv.mnemonic_code LIKE CONCAT('%', :mnemonic_code, '%'))
  AND IF(:is_group IS NULL OR :is_group = '', 1, pv.is_group_product = :is_group)
  AND IF(:bom_type IS NULL OR :bom_type = '', 1, pv.bom_type = :bom_type)
  AND IF(:product_version IS NULL OR :product_version = '', 1, pv.product_version = :product_version)
  AND IF(:product_size IS NULL OR :product_size = '', 1, pv.product_size LIKE CONCAT('%', :product_size, '%'))
  
  -- 时间条件
  AND IF(:startcreatetime IS NULL OR :startcreatetime = '', 1, pv.create_time >= :startcreatetime)
  AND IF(:endcreatetime IS NULL OR :endcreatetime = '', 1, pv.create_time <= :endcreatetime)
  AND IF(:startupdatetime IS NULL OR :startupdatetime = '', 1, pv.update_time >= :startupdatetime)
  AND IF(:endupdatetime IS NULL OR :endupdatetime = '', 1, pv.update_time <= :endupdatetime)
  
  -- 分类条件
  AND IF(:main_class_size > 0, pv.main_class IN (:main_class), 1)
  AND IF(:sub_class_size > 0, pv.sub_class IN (:sub_class), 1)
  
  -- 关联表条件：只在需要时执行EXISTS
  AND IF(:client_name IS NULL OR :client_name = '', 1,
    EXISTS (SELECT 1 FROM pdm_product_client pc 
            WHERE pc.product_code = pv.material_code 
              AND pc.client_name LIKE CONCAT('%', :client_name, '%')))
              
  AND IF(:mater_code IS NULL OR :mater_code = '', 1,
    EXISTS (SELECT 1 FROM pdm_product_bom pb 
            WHERE pb.product_code = pv.material_code 
              AND pb.product_version = pv.product_version
              AND pb.material_code LIKE CONCAT('%', :mater_code, '%')))
              
  AND IF(:mater_name IS NULL OR :mater_name = '', 1,
    EXISTS (SELECT 1 FROM pdm_product_bom pb 
            WHERE pb.product_code = pv.material_code 
              AND pb.product_version = pv.product_version
              AND pb.material_name LIKE CONCAT('%', :mater_name, '%')))
              
  AND IF(:paper_size IS NULL OR :paper_size = '', 1,
    EXISTS (SELECT 1 FROM pdm_product_bom pb 
            WHERE pb.product_code = pv.material_code 
              AND pb.product_version = pv.product_version
              AND pb.component_size LIKE CONCAT('%', :paper_size, '%')
              AND pb.categroy = '1'))
              
  AND IF(:processname IS NULL OR :processname = '', 1,
    EXISTS (SELECT 1 FROM pdm_product_craft ppc 
            WHERE ppc.product_code = pv.material_code 
              AND ppc.product_version = pv.product_version
              AND ppc.craft_name LIKE CONCAT('%', :processname, '%')))
              
  AND IF(:fileFlag = '1',
    EXISTS (SELECT 1 FROM pdm_upload_file pf 
            WHERE pf.material_code = pv.material_code 
              AND pf.product_version = pv.product_version 
              AND pf.file_id IS NOT NULL), 1)
ORDER BY pv.id DESC
LIMIT 10;

-- ========================================
-- 性能对比说明
-- ========================================
/*
原始查询问题：
1. 无论参数是否有值，都会执行所有LEFT JOIN
2. 扫描行数：pv表50053行，pc表25992行
3. 使用临时表和文件排序

条件逻辑优化后的优势：
1. 只在参数有值时才执行相关的EXISTS子查询
2. 避免了不必要的JOIN操作
3. 减少了临时表的使用
4. 提高了索引的使用效率
5. 大幅减少了扫描的行数

预期性能提升：
- 当大部分搜索参数为空时，性能提升最为明显
- 扫描行数可能从50000+行减少到几百行
- 查询时间可能从几秒减少到几十毫秒
*/
