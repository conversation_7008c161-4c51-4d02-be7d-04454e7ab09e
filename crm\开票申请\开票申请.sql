-- 开票申请
select status,t1.* from invoice_application t1 where apply_no='VR202507185337';
-- 开票申请详情
select invoice_include_price,exchange_rate,apply_invoice_quantity,tax_rate,tax_inclusive,status,t.*
from invoice_application_detail t where split_order_no='25060091A' ;
-- 开票申请日志
select * from invoice_operation_record where apply_no='VR202506224255';
-- 出库记录
select status,enable_num,ship_quantity,actual_ship_quantity,bood.*
from bip_outbound_order_detail bood where split_order_no='25060091A';
-- 开票和出库情况
select bood.outbound_date,enable_num,bood.actual_ship_quantity, bood.*,csid.*
from bip_outbound_order_detail bood
                     left join (select csrcid, csrcbid, sum(nnum) nnum
                                from crm_sales_invoice_details
                                where flag_deleted = 0
                                group by csrcid, csrcbid) csid
                               on bood.outbound_header = csid.csrcid and bood.outbound_line_id = csid.csrcbid
            where bood.flag_deleted=0
and bood.split_order_no='25060091A'
order by bood.outbound_date asc;

select * from crm_sales_invoice_details csid where csrcbid='1001A5100000011LUN8I';

-- 查询开票设置
select * from crm_sales_settings  where flag_deleted = 0;
-- 开票申请记录
select * from invoice_operation_record ior where create_time like '2025-06-26%';


select *
from invoice_application_detail
where status in ('0', '1', '3', '4')
  and flag_deleted = 0
  and apply_no != 'VR202506284550'
  and outbound_line_id = '1001A5100000012XWBVD';

select a.*,
       c.unit_price_excluding_usd,
       c.unit_price_usd,
       c.tax_inclusive,
       c.commission_print_number,
       c.quotation_factory
from crm_sales_order_product a
         left join crm_contract_management_product b on a.contract_management_code
                                                            = b.contract_management_code and
                                                        a.contract_product_line_number =
                                                        b.contract_product_line_number and
                                                        b.flag_deleted = 0
         left join crm_preliminary_quotation_product c on b.quotation_product_id = c.id and c.flag_deleted = 0
where a.flag_deleted = 0
  and a.sales_order_code in ('25060249A');


SELECT
    bood.*,
    CASE
        WHEN csop.stocked_product IS NULL OR csop.stocked_product = '' THEN '否'
        ELSE csop.stocked_product
        END AS stocked_product
FROM
    bip_outbound_order_detail bood
        LEFT JOIN
    crm_sales_order_product csop ON
        bood.split_order_no = csop.sales_order_code AND
        bood.product_code = csop.material_code AND
        bood.split_order_line_no = csop.contract_product_line_number
WHERE
    bood.enable_num > 0
  AND bood.ship_quantity > 0
  and bood.status = '0'
and bood.split_order_no='25060249A'
;
select 28840-30-21;
