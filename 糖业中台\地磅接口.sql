drop table ods_material_weighing;
create table ods_material_weighing
(
    id                                 int auto_increment
        primary key,
    sugarcane_leaf_purchase_quantity decimal(18, 2) comment '蔗叶收购量',
    sugarcane_impurities             decimal(18, 2) comment '甘蔗夹杂物',
    sugar_quantity            decimal(18, 2) comment '当日暂存在FN原糖仓的原糖量',
    update_time                        varchar(20) comment '更新时间',
    unique index update_time_index (update_time)
) comment '地磅系统_物资过磅';

select * from ods_material_weighing order by update_time desc;
delete from ods_material_weighing  where update_time is null;
select datediff('2025-07-14','2024-11-17');
