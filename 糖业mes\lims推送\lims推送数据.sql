-- 查询班次
SELECT classes_start_time,classes_end_time,
       CASE
           WHEN classes_end_time < classes_start_time THEN 1
           ELSE 0
           END AS is_cross_day_shift
FROM mdm_shift_info
WHERE status = 1 and flag_deleted = 0
  AND (
    (classes_end_time >= classes_start_time AND
     CURRENT_TIME() BETWEEN classes_start_time AND classes_end_time)
        OR
    (classes_end_time < classes_start_time AND
     (CURRENT_TIME() >= classes_start_time OR
      CURRENT_TIME() <= classes_end_time))
    );
-- 查询推送记录
SELECT * FROM exsystem_push_record
WHERE sys_name = 'lims'
  AND task_code = '榨蔗量(DCS)'
  AND flag_push = '1'
  AND (
    (
        TIME(:classes_start_time) <= TIME(:classes_end_time)
            AND TIME(push_time) BETWEEN TIME(:classes_start_time) AND TIME(:classes_end_time)
            AND DATE(push_time) = CURRENT_DATE
        )
        OR
    (
        TIME(:classes_start_time) > TIME(:classes_end_time)
            AND (
            (TIME(push_time) >= TIME(:classes_start_time) AND DATE(push_time) = CURRENT_DATE)
                OR
            (TIME(push_time) <= TIME(:classes_end_time) AND DATE(push_time) = CURRENT_DATE + INTERVAL 1 DAY)
            )
        )
    );

SELECT
    *
FROM
    weighbridge_gather_info
WHERE
    flag_deleted = 0
  AND (
    (
        :classes_start_time <= :classes_end_time
            AND CAST(update_time AS TIME) BETWEEN :classes_start_time AND :classes_end_time
            AND CAST(update_time AS DATE) = CURRENT_DATE
        )
        OR
    (
        :classes_start_time > :classes_end_time
            AND (
            (CAST(update_time AS TIME) >= :classes_start_time AND CAST(update_time AS DATE) = CURRENT_DATE - 1)
                OR
            (CAST(update_time AS TIME) <= :classes_end_time AND CAST(update_time AS DATE) = CURRENT_DATE )
            )
        )
    )
order by update_time asc
