-- 已入库数量
-- 6876	折叠纸盒类 cartonID
-- 6901	说明书类 explainID
WITH aggregated_data AS (
    SELECT
        DATE_FORMAT(finish_date, '%Y-%m') AS month_sum,
        SUM(CASE WHEN large_category = :cartonID THEN received_quantity ELSE 0 END) AS sum_carton_total,
        SUM(CASE WHEN large_category = :cartonID AND received_quantity < 10000 THEN received_quantity ELSE 0 END) AS sum_carton_one,
        SUM(CASE WHEN large_category = :cartonID AND received_quantity >= 10000 AND received_quantity < 20000 THEN received_quantity ELSE 0 END) AS sum_carton_two,
        SUM(CASE WHEN large_category = :cartonID AND received_quantity >= 20000 AND received_quantity < 50000 THEN received_quantity ELSE 0 END) AS sum_carton_five,
        SUM(CASE WHEN large_category = :cartonID AND received_quantity >= 50000 AND received_quantity < 200000 THEN received_quantity ELSE 0 END) AS sum_carton_twenty,
        SUM(CASE WHEN large_category = :cartonID AND received_quantity >= 200000 THEN received_quantity ELSE 0 END) AS sum_carton_twenty_up,
        SUM(CASE WHEN large_category = :explainID THEN received_quantity ELSE 0 END) AS sum_explain_total,
        SUM(CASE WHEN large_category = :explainID AND received_quantity < 10000 THEN received_quantity ELSE 0 END) AS sum_explain_one,
        SUM(CASE WHEN large_category = :explainID AND received_quantity >= 10000 AND received_quantity < 50000 THEN received_quantity ELSE 0 END) AS sum_explain_five,
        SUM(CASE WHEN large_category = :explainID AND received_quantity >= 50000 AND received_quantity < 400000 THEN received_quantity ELSE 0 END) AS sum_explain_forty,
        SUM(CASE WHEN large_category = :explainID AND received_quantity >= 400000 THEN received_quantity ELSE 0 END) AS sum_explain_forty_up
    FROM
        pm_job_order
    WHERE
        flag_deleted = 0
            AND production_order_status = '3'
            AND source_type = '1'
            AND (large_category = :cartonID OR large_category = :explainID)
            AND YEAR(finish_date) = :yearL
GROUP BY
    month_sum
    )
SELECT
    month_sum,
    sum_carton_total,
    sum_carton_one,
    sum_carton_two,
    sum_carton_five,
    sum_carton_twenty,
    sum_carton_twenty_up,
    sum_explain_total,
    sum_explain_one,
    sum_explain_five,
    sum_explain_forty,
    sum_explain_forty_up
FROM
    aggregated_data;


-- 获取类型，月份，批次号 优化后
SELECT
    CASE
        WHEN large_category = :cartonID THEN 'carton'
        WHEN large_category = :explainID THEN 'explain'
        END AS large_category,
    DATE_FORMAT(finish_date, '%Y-%m') AS month_sum,
    production_batch_number
FROM
    pm_job_order
WHERE
    flag_deleted = 0
  AND production_order_status = '3'
  AND source_type = '1'
  AND large_category IN (:cartonID, :explainID)
  AND YEAR(finish_date) = :yearL
GROUP BY
    large_category,
    month_sum,
    production_batch_number;

--
WITH task_code AS (
    SELECT
        DATE_FORMAT(pjo.finish_date, '%Y-%m') AS month_sum,
        pjom.task_code
    FROM
        pm_job_order pjo
            LEFT JOIN pm_job_order_management pjom ON
            pjom.production_batch_number = pjo.production_batch_number
    WHERE
        pjo.flag_deleted = 0
      AND pjom.flag_deleted = 0
      AND pjo.is_collage = '1'
      AND pjo.source_type = '1'
      AND pjo.production_order_status = '3'
      AND IF(:gtTrueL, pjo.received_quantity >= :gtNumL, 1 = 1)
      AND IF(:ltTrueL, pjo.received_quantity < :ltNumL, 1 = 1)
      AND pjo.large_category = :cateGoryL
      AND YEAR(pjo.finish_date) = :yearL
    UNION
    SELECT
        DATE_FORMAT(pjo.finish_date, '%Y-%m') AS month_sum,
        pjom.task_code
    FROM
        pm_job_order pjo
            LEFT JOIN pm_job_order_middle pjomi ON
            pjo.production_batch_number = pjomi.production_batch_number
            LEFT JOIN pm_job_order_management pjom ON
            pjom.task_code = pjomi.task_code
    WHERE
        pjo.flag_deleted = 0
      AND pjom.flag_deleted = 0
      AND pjo.is_collage = '0'
      AND pjo.source_type = '1'
      AND pjo.production_order_status = '3'
      AND IF(:gtTrueL, pjo.received_quantity >= :gtNumL, 1 = 1)
      AND IF(:ltTrueL, pjo.received_quantity < :ltNumL, 1 = 1)
      AND pjo.large_category = :cateGoryL
      AND YEAR(pjo.finish_date) = :yearL
),
     feed_num AS (
         SELECT
             tc.month_sum,
             sum(pfbja.feed_quantity) AS :feedNameL,
                 :feedNameL AS feedNameL
         FROM
             task_code tc
                 LEFT JOIN pm_job_detail pjd ON
                 tc.task_code = pjd.task_code
                     AND pjd.flag_deleted = 0
                     AND (pjd.before_process_code = '' OR pjd.before_process_code IS NULL)
                 LEFT JOIN pm_final_batch_job_audit pfbja ON
                 pjd.id = pfbja.job_strip_number
         WHERE
             pfbja.flag_deleted = 0
         GROUP BY
             tc.month_sum
     )
SELECT
    *
FROM
    feed_num cfn;
