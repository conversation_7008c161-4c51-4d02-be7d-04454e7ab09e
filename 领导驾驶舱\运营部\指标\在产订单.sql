-- ETL: 运营部-在产订单->dwd_yunying_shuomingshu
-- 在产订单:生产中(全部下达-成品入库)的订单的总金额
select 8                                       id,
       if(value < 0, 0, value)                 value,
       DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s') time,
       '在产订单'                              name
from (SELECT sum(t.VALUE) value
      FROM (SELECT COALESCE(ROUND(SUM((COALESCE(a.release_quantity) - COALESCE(t2.report_num, 0)) *
                                      COALESCE(b.unit_price_no, 0)) / 10000, 0), 0) AS
                       VALUE
            FROM pm_job_order a
                     LEFT JOIN pm_order_product b ON a.order_code = b.sales_number
                AND a.material_code = b.product_number
                     LEFT JOIN pm_job_order_management t ON a.production_batch_number = t.production_batch_number
                     LEFT join (select pro_batch, sum(report_num) as report_num
                                from pm_finsih_project_stock
                                where flag_deleted = 0
                                  and stock_type in (1, 4)
                                  and storage_status = 4
                                group by pro_batch) t2 on a.production_batch_number = t2.pro_batch
            WHERE a.production_order_status = '2'
              and t.STATUS = 2
              AND a.is_collage = 1
              AND a.source_type = 1
              and a.flag_deleted = 0
              and b.flag_deleted = 0
              and t.flag_deleted = 0
            UNION
            SELECT COALESCE(ROUND(SUM((COALESCE(d.release_quantity) - COALESCE(t2.report_num, 0)) *
                                      COALESCE(e.unit_price_no, 0)) / 10000, 0), 0) AS
                       VALUE

            FROM pm_job_order_management a
                     LEFT JOIN pm_job_order b ON b.production_batch_number = a.production_batch_number
                     RIGHT JOIN pm_jigsaw_of_product c ON b.order_code = c.order_number
                     LEFT JOIN pm_job_order d ON c.order_number = d.order_code
                     LEFT JOIN pm_order_product e ON d.order_code = e.sales_number
                     LEFT join (select pro_batch, sum(report_num) as report_num
                                from pm_finsih_project_stock
                                where flag_deleted = 0
                                  and stock_type in (1, 4)
                                  and storage_status = 4
                                group by pro_batch) t2 on a.production_batch_number = t2.pro_batch
            WHERE a.is_collage = 0
              AND a.STATUS = 2
              AND d.material_code = e.product_number
              AND d.source_type = 1
              and a.flag_deleted = 0
              and b.flag_deleted = 0
              and c.flag_deleted = 0
              and d.flag_deleted = 0
              and e.flag_deleted = 0) t) temp;
select sum(value) value ,
                            sum(quantity) quantity,
                            major_categories from
(
SELECT COALESCE(ROUND(SUM((COALESCE(a.release_quantity) - COALESCE(t2.report_num, 0)) *
                          COALESCE(b.unit_price_no, 0)) / 10000, 0), 0) AS
           VALUE,
       round(SUM((COALESCE(a.release_quantity) - COALESCE(t2.report_num, 0)))/10000,0) quantity,
      a.large_category major_categories
FROM pm_job_order a
         LEFT JOIN pm_order_product b ON a.order_code = b.sales_number
    AND a.material_code = b.product_number
         LEFT JOIN pm_job_order_management t ON a.production_batch_number = t.production_batch_number
         LEFT join (select pro_batch, sum(report_num) as report_num
                    from pm_finsih_project_stock
                    where flag_deleted = 0
                      and stock_type in (1, 4)
                      and storage_status = 4
                    group by pro_batch) t2 on a.production_batch_number = t2.pro_batch
WHERE a.production_order_status = '2'
  and t.STATUS = 2
  AND a.is_collage = 1
  AND a.source_type = 1
  and a.flag_deleted = 0
  and b.flag_deleted = 0
  and t.flag_deleted = 0
  and a.large_category is not null
group by a.large_category
UNION
SELECT COALESCE(ROUND(SUM((COALESCE(d.release_quantity) - COALESCE(t2.report_num, 0)) *
                          COALESCE(e.unit_price_no, 0)) / 10000, 0), 0) AS
                        VALUE,
       round(SUM((COALESCE(d.release_quantity) - COALESCE(t2.report_num, 0)))/10000,0) AS
                        quantity,
       b.large_category major_categories
FROM pm_job_order_management a
         LEFT JOIN pm_job_order b ON b.production_batch_number = a.production_batch_number
         RIGHT JOIN pm_jigsaw_of_product c ON b.order_code = c.order_number
         LEFT JOIN pm_job_order d ON c.order_number = d.order_code
         LEFT JOIN pm_order_product e ON d.order_code = e.sales_number
         LEFT join (select pro_batch, sum(report_num) as report_num
                    from pm_finsih_project_stock
                    where flag_deleted = 0
                      and stock_type in (1, 4)
                      and storage_status = 4
                    group by pro_batch) t2 on a.production_batch_number = t2.pro_batch
WHERE a.is_collage = 0
  AND a.STATUS = 2
  AND d.material_code = e.product_number
  AND d.source_type = 1
  and a.flag_deleted = 0
  and b.flag_deleted = 0
  and c.flag_deleted = 0
  and d.flag_deleted = 0
  and e.flag_deleted = 0
  and b.large_category is not null
group by b.large_category)temp group by major_categories
;


SELECT
    JSON_ARRAYAGG(
            JSON_OBJECT(
                    'value', value,
                    'quantity', quantity,
                    'major_categories', major_categories
            )
    ) AS                                    value,
    DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s') time,
    8                                       id,
    '在产订单'                              name
FROM (select sum(value) value ,
             sum(quantity) quantity,
             major_categories from
          (
              SELECT COALESCE(ROUND(SUM((COALESCE(a.release_quantity) - COALESCE(t2.report_num, 0)) *
                                        COALESCE(b.unit_price_no, 0)) / 10000, 0), 0) AS
                                                                                                     VALUE,
                     round(SUM((COALESCE(a.release_quantity) - COALESCE(t2.report_num, 0)))/10000,0) quantity,
                     a.large_category major_categories
              FROM pm_job_order a
                       LEFT JOIN pm_order_product b ON a.order_code = b.sales_number
                  AND a.material_code = b.product_number
                       LEFT JOIN pm_job_order_management t ON a.production_batch_number = t.production_batch_number
                       LEFT join (select pro_batch, sum(report_num) as report_num
                                  from pm_finsih_project_stock
                                  where flag_deleted = 0
                                    and stock_type in (1, 4)
                                    and storage_status = 4
                                  group by pro_batch) t2 on a.production_batch_number = t2.pro_batch
              WHERE a.production_order_status = '2'
                and t.STATUS = 2
                AND a.is_collage = 1
                AND a.source_type = 1
                and a.flag_deleted = 0
                and b.flag_deleted = 0
                and t.flag_deleted = 0
                and a.large_category is not null
              group by a.large_category
              UNION
              SELECT COALESCE(ROUND(SUM((COALESCE(d.release_quantity) - COALESCE(t2.report_num, 0)) *
                                        COALESCE(e.unit_price_no, 0)) / 10000, 0), 0) AS
                                      VALUE,
                     round(SUM((COALESCE(d.release_quantity) - COALESCE(t2.report_num, 0)))/10000,0) AS
                                      quantity,
                     b.large_category major_categories
              FROM pm_job_order_management a
                       LEFT JOIN pm_job_order b ON b.production_batch_number = a.production_batch_number
                       RIGHT JOIN pm_jigsaw_of_product c ON b.order_code = c.order_number
                       LEFT JOIN pm_job_order d ON c.order_number = d.order_code
                       LEFT JOIN pm_order_product e ON d.order_code = e.sales_number
                       LEFT join (select pro_batch, sum(report_num) as report_num
                                  from pm_finsih_project_stock
                                  where flag_deleted = 0
                                    and stock_type in (1, 4)
                                    and storage_status = 4
                                  group by pro_batch) t2 on a.production_batch_number = t2.pro_batch
              WHERE a.is_collage = 0
                AND a.STATUS = 2
                AND d.material_code = e.product_number
                AND d.source_type = 1
                and a.flag_deleted = 0
                and b.flag_deleted = 0
                and c.flag_deleted = 0
                and d.flag_deleted = 0
                and e.flag_deleted = 0
                and b.large_category is not null
              group by b.large_category)temp group by major_categories



           ) AS subquery;


