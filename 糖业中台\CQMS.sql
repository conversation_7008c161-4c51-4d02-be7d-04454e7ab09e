-- CQMS系统数据
drop table if exists ods_cqms_metric;
CREATE TABLE ods_cqms_metric
(
    -- 主键ID
    id                           BIGINT PRIMARY KEY AUTO_INCREMENT,
    -- 基础信息
    season                       VARCHAR(10)    NOT NULL COMMENT '榨季',
    factory_code                 VARCHAR(10)    NOT NULL COMMENT '工厂代码',
    entry_date                   varchar(20)    NOT NULL COMMENT '进厂时间',
    -- 人工砍蔗数据
    manual_purity                DECIMAL(16, 8) NOT NULL COMMENT '人工砍蔗甘蔗简纯度',
    manual_sugar_content         DECIMAL(16, 8) NOT NULL COMMENT '人工砍蔗甘蔗糖分',
    -- 机收蔗数据
    machine_purity               DECIMAL(16, 8) NOT NULL COMMENT '机收蔗甘蔗简纯度',
    machine_sugar_content        DECIMAL(16, 8) NOT NULL COMMENT '机收蔗甘蔗糖分',
    -- 榨季累计数据（人工砍蔗）
    season_manual_purity         DECIMAL(16, 8) NOT NULL COMMENT '当日榨季累计人工砍蔗简纯度',
    season_manual_sugar_content  DECIMAL(16, 8) NOT NULL COMMENT '当日榨季累计人工砍蔗甘蔗糖分',
    -- 榨季累计数据（机收蔗）
    season_machine_purity        DECIMAL(16, 8) NOT NULL COMMENT '当日榨季累计机收蔗简纯度',
    season_machine_sugar_content DECIMAL(16, 8) NOT NULL COMMENT '当日榨季累计机收蔗甘蔗糖分',
    -- 甘蔗品种相关
    variety_code                 VARCHAR(50)    NOT NULL COMMENT '甘蔗品种代码',
    variety_name                 VARCHAR(50)    NOT NULL COMMENT '甘蔗品种名称',
    variety_daily_sugar          DECIMAL(16, 8) NOT NULL COMMENT '各甘蔗品种当日糖分',
    variety_season_sugar         DECIMAL(16, 8) NOT NULL COMMENT '各甘蔗品种榨季累计糖分',
    -- 总糖分
    total_sugar_content          DECIMAL(16, 8) NOT NULL COMMENT '甘蔗糖分',
    -- 索引
    INDEX idx_crushing_season (season),
    INDEX idx_factory_code (factory_code),
    INDEX idx_entry_date (entry_date),
    INDEX idx_variety_name (variety_name),
    INDEX idx_variety_code (variety_code)
) COMMENT ='CQMS_质量数据接口';
select * from ods_cqms_metric;
truncate table ods_cqms_metric;
select max(manual_purity) manual_purity from ods_cqms_metric ocm
where entry_date=date_format(now(),'%Y-%m-%d');
;

select variety_name,
       variety_daily_sugar,
       variety_season_sugar
from ods_cqms_metric ofm
;
