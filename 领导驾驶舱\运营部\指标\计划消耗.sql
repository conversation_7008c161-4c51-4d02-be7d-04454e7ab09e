-- 根据批次查询折叠纸盒和说明书的作业
SELECT
    optl.task_code task_code,
    optl.process_consumption process_consumption,
    optl.post_process_consumption post_process_consumption,
    optl.out_of_book out_of_book
FROM
    (
        SELECT
            DISTINCT
            opjo.production_batch_number
        FROM
            cockpit.ods_pm_job_order opjo
        WHERE
            opjo.flag_deleted = 0
          AND opjo.large_category IN (6876, 6901)
    ) bd
        LEFT JOIN cockpit.ods_pm_task_list optl ON
        optl.production_batch_number = bd.production_batch_number
 ;

-- 根据批次查询折叠纸盒和说明书的作业 新
select  task_code,process_consumption,post_process_consumption,out_of_book
from cockpit.ods_pm_task_list t1 where
t1.production_batch_number in (
    SELECT
        DISTINCT
        opjo.production_batch_number
    FROM
        cockpit.ods_pm_job_order opjo
    WHERE
        opjo.flag_deleted = 0
      AND opjo.large_category IN (6876, 6901)
    ) ;


SELECT
    opjd.task_code,
    opjd.process_code,
    opjd.process_type,
    opjom.release_quantity
FROM
    (
        SELECT
            MIN(opjd.id) opjd_id
        FROM
            ods_pm_job_detail opjd
        WHERE
            opjd.flag_deleted = 0
        GROUP BY
            opjd.task_code
    ) jd
        LEFT JOIN ods_pm_job_detail opjd ON
        opjd.id = jd.opjd_id
        LEFT JOIN ods_pm_job_order_management opjom ON
        opjom.task_code = opjd.task_code
WHERE
    opjd.flag_deleted = 0
  AND opjom.flag_deleted = 0
;

select  t1.task_code,process_consumption,post_process_consumption,out_of_book,
        t2.process_type,release_quantity
from cockpit.ods_pm_task_list t1
left join cockpit.dwd_task_release t2 on t1.task_code=t2.task_code
where
    t1.production_batch_number in (
        SELECT
            DISTINCT
            opjo.production_batch_number
        FROM
            cockpit.ods_pm_job_order opjo
        WHERE
            opjo.flag_deleted = 0
          AND opjo.large_category IN (6876, 6901)
    ) ;
    ;
SHOW PROCESSLIST;
