-- ETL:CRM纸张预测明细
select year(t1.create_time) update_year,
       t1.factory_assigned production_factory,
       t1.cust_manager_code ,
       t1.cust_manager_name responsible_person,
       t1.cust_code,
       t1.cust_name,
       t2.material_code product_code,
       t2.material_name product_name,
       t2.unit_price_exclusive,
       t2.amount_exclusive_tax,
       t3.paper_code,
       t3.paper_name,
       t3.gram_weight,
       sum(if(month(t1.create_time) = 1, product_quantity, 0))  num_1,
       sum(if(month(t1.create_time) = 2, product_quantity, 0))  num_2,
       sum(if(month(t1.create_time) = 3, product_quantity, 0))  num_3,
       sum(if(month(t1.create_time) = 4, product_quantity, 0))  num_4,
       sum(if(month(t1.create_time) = 5, product_quantity, 0))  num_5,
       sum(if(month(t1.create_time) = 6, product_quantity, 0))  num_6,
       sum(if(month(t1.create_time) = 7, product_quantity, 0))  num_7,
       sum(if(month(t1.create_time) = 8, product_quantity, 0))  num_8,
       sum(if(month(t1.create_time) = 9, product_quantity, 0))  num_9,
       sum(if(month(t1.create_time) = 10, product_quantity, 0)) num_10,
       sum(if(month(t1.create_time) = 11, product_quantity, 0)) num_11,
       sum(if(month(t1.create_time) = 12, product_quantity, 0)) num_12
from crm_sales_order t1
left join crm_sales_order_product t2 on t1.sales_order_code = t2.sales_order_code and t2.flag_deleted = 0
left join (
    select t1.quotation_code,
           t1.preliminary_quotation_code,
           t1.material_code,
           t1.material_name,
           t2.paper_code,
           t2.paper_name,
           t2.gram_weight
    from crm_preliminary_quotation_product t1
             join (
        select quotation_code,
               quotation_version,
               material_code paper_code,
               material_name paper_name,
               replace(gram_weight,'g','') gram_weight
        from (
                 select quotation_code,
                        quotation_version,
                        material_code,
                        material_name,
                        REGEXP_SUBSTR(material_name, '[0-9]+g') AS gram_weight,
                        create_time,
                        ROW_NUMBER() OVER (PARTITION BY quotation_code ORDER BY quotation_version DESC) as rn
                 from crm_fixed_cost_paper
                 where categroy=1 and flag_deleted=0
             ) t
        where rn = 1
    )t2 on t1.quotation_code=t2.quotation_code
    where t1.flag_deleted=0
)t3 on t2.material_code=t3.material_code and t1.quotation_code=t3.preliminary_quotation_code
where t1.flag_deleted=0
group by
    year(t1.create_time),
       t1.factory_assigned,
       t1.cust_manager_code,
       t1.cust_manager_name,
       t1.cust_code,
       t1.cust_name,
       t2.material_code,
       t2.material_name,
       t2.unit_price_exclusive,
       t2.amount_exclusive_tax,
       t3.paper_code,
       t3.paper_name,
       t3.gram_weight
;
-- 更新工号
select t1.id, t1.cust_manager_code,t1.responsible_person,t2.username from
    dwd_paper_forecast_detail t1
        join ods_sys_user t2 on t1.cust_manager_code=t2.user_id and t2.del_flag=0
;
update dwd_paper_forecast_detail t1
    join ods_sys_user t2 on t1.cust_manager_code=t2.user_id and t2.del_flag=0
set t1.work_code=t2.username,t1.responsible_person=t2.name where 1=1;
select * from dwd_paper_forecast_detail;


-- 获取报价单产品和纸张关联
select t1.quotation_code,
       t1.preliminary_quotation_code,
       t1.material_code,
       t1.material_name,
       t2.paper_code,
       t2.paper_name,
       t2.create_time
from crm_preliminary_quotation_product t1
join (
    select quotation_code,
           quotation_version,
           material_code paper_code,
           material_name paper_name,
           gram_weight,
           create_time
    from (
             select quotation_code,
                    quotation_version,
                    material_code,
                    material_name,
                    REGEXP_SUBSTR(material_name, '[0-9]+g') AS gram_weight,
                    create_time,
                    ROW_NUMBER() OVER (PARTITION BY quotation_code ORDER BY quotation_version DESC) as rn
             from crm_fixed_cost_paper
             where categroy=1 and flag_deleted=0
         ) t
    where rn = 1
)t2 on t1.quotation_code=t2.quotation_code
where t1.flag_deleted=0
order by t2.create_time desc
;


-- 核价单纸张数据获取
select quotation_code,
       quotation_version,
       material_code paper_code,
       material_name paper_name,
       replace(gram_weight,'g','') gram_weight,
       create_time
from (
    select quotation_code,
           quotation_version,
           material_code,
           material_name,
           REGEXP_SUBSTR(material_name, '[0-9]+g') AS gram_weight,
           create_time,
           ROW_NUMBER() OVER (PARTITION BY quotation_code ORDER BY quotation_version DESC) as rn
    from crm_fixed_cost_paper
    where categroy=1 and flag_deleted=0
) t
where rn = 1
;

drop table dwd_paper_forecast_detail;
CREATE TABLE dwd_paper_forecast_detail
(
    id                 INT AUTO_INCREMENT PRIMARY KEY COMMENT 'id',
    update_year        varchar(4)  not null comment '更新年份',
    production_factory varchar(10) COMMENT '生产工厂',
    work_code          VARCHAR(20) NOT NULL COMMENT '业务员工号',
    cust_manager_code varchar(50) NOT NULL COMMENT '负责人编码',
    responsible_person VARCHAR(100) COMMENT '业务员姓名',
    cust_code          VARCHAR(50) COMMENT '客户编号',
    cust_name          VARCHAR(255) COMMENT '客户名称',
    product_code       VARCHAR(50) COMMENT '产品编码',
    product_name       VARCHAR(255) COMMENT '产品名称',
    unit_sales_price   varchar(50) COMMENT '产品的单价',
    paper_code         VARCHAR(50) COMMENT '纸张编码',
    paper_name         VARCHAR(255) COMMENT '纸张名称',
    gram_weight        INT COMMENT '克重',
    jan_production     INT COMMENT '1月生产数量',
    feb_production     INT COMMENT '2月生产数量',
    mar_production     INT COMMENT '3月生产数量',
    apr_production     INT COMMENT '4月生产数量',
    may_production     INT COMMENT '5月生产数量',
    jun_production     INT COMMENT '6月生产数量',
    jul_production     INT COMMENT '7月生产数量',
    aug_production     INT COMMENT '8月生产数量',
    sep_production     INT COMMENT '9月生产数量',
    oct_production     INT COMMENT '10月生产数量',
    nov_production     INT COMMENT '11月生产数量',
    dec_production     INT COMMENT '12月生产数量'
) comment '纸张预测明细';

select id,
       update_year,
       production_factory,
       work_code,
       responsible_person,
       cust_code,
       cust_name,
       product_code,
       product_name,
       unit_sales_price,
       paper_code,
       paper_name,
       gram_weight,
       jan_production,
       feb_production,
       mar_production,
       apr_production,
       may_production,
       jun_production,
       jul_production,
       aug_production,
       sep_production,
       oct_production,
       nov_production,
       dec_production
from dwd_paper_forecast_detail
where update_year = YEAR(DATE_SUB(CURDATE(), INTERVAL 1 YEAR))
# and work_code=:work_code
;


select * from dwd_paper_forecast_detail dpfd where work_code='1744971763818213377';
select id,
       update_year,
       production_factory,
       work_code,
       responsible_person,
       cust_code,
       cust_name,
       product_code,
       product_name,
       unit_sales_price,
       paper_code,
       paper_name,
       gram_weight,
       jan_production,
       feb_production,
       mar_production,
       apr_production,
       may_production,
       jun_production,
       jul_production,
       aug_production,
       sep_production,
       oct_production,
       nov_production,
       dec_production
from cockpit.dwd_paper_forecast_detail
where update_year = LEFT(:select_month, 4) - 1
  AND if(:admin,
         1,
         if(:cust_code_size > 0, cust_code in (:cust_code_arr), 1)
      );
