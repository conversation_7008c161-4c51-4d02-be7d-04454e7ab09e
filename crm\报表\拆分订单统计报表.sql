-- 拆分订单统计
SELECT
      '西安环球'                                                                                           sale_company,
       t3.deparment_code,
       t3.deparment_name,
       t3.department_region region,
       t3.cust_name,
       t3.cust_manager_code,
       t3.cust_manager_name,
       t3.sales_assistant_code,
       t3.sales_assistant_name,
       t3.cust_type,
       CASE
           WHEN t3.cust_type = 0 THEN '新建客户'
           WHEN t3.cust_type = 1 THEN '公海客户'
           WHEN t3.cust_type = 2 THEN '合作客户'
           WHEN t3.cust_type = 3 THEN '开发中客户'
           WHEN t3.cust_type = 4 THEN '受限客户'
           END                                                                                              cust_type_name,
       t1.sales_order_code,
       STR_TO_DATE(t1.create_time, '%Y-%m-%d')                                                              create_time,
       t2.main_class,
       t2.sub_class,
       t2.material_code,
       t2.material_name,
       t2.product_version,
       t2.order_quantity_after_split    product_quantity,
       round(IF(t2.unit_price_exclusive NOT REGEXP '^-?[0-9]+(\\.[0-9]+)?$', 0,
                t2.unit_price_exclusive ),
             4)                                                                                             unit_price_exclusive,
       round(IF(t2.amount_exclusive_tax NOT REGEXP '^-?[0-9]+(\\.[0-9]+)?$', 0,
                t2.amount_exclusive_tax ),
             2)                                                                                             amount_exclusive_tax,
       round(IF(t2.unit_price NOT REGEXP '^-?[0-9]+(\\.[0-9]+)?$', 0, t2.unit_price ), 4) unit_price,
       round(IF(t2.amount_tax_inclusive NOT REGEXP '^-?[0-9]+(\\.[0-9]+)?$', 0,
                t2.amount_tax_inclusive ),
             2)                                                                                             amount_tax_inclusive,
       t2.settlement_currency,
       round(IF(t2.settlement_currency_price_exclusive NOT REGEXP '^-?[0-9]+(\\.[0-9]+)?$', 0,
                t2.settlement_currency_price_exclusive ),
             4)                                                                                             settlement_currency_price_exclusive,
       round(IF(t2.settlement_currency_amount_exclusive NOT REGEXP '^-?[0-9]+(\\.[0-9]+)?$', 0,
                t2.settlement_currency_amount_exclusive ),
             2)                                                                                             settlement_currency_amount_exclusive,
       t1.factory_assigned,
       t1.quotation_code,
       t1.status,
      case
          when t1.status=10 then '已删除'
          when t1.status=8 then '已取消'
          when t1.status=7 then '已关闭'
          when t1.status=6 then '已开票'
          when (select count(*)
                from invoice_application ia
                where ia.status IN ('0', '3', '4')
                  and ia.flag_deleted = 0
                  and ia.split_order_no like concat('%', t1.sales_order_code, '%')) > 0
              then '已发货未开票'
          when t1.status=0 then '已拆分'
          when t1.status=1 then '已下达'
          when t1.status=2 then '已排产'
          when t1.status=3 then '已领料'
          when t1.status=4 then '生产中'
          when t1.status=5 then '已入库'
          else '其他'
          end status_desc,
          t2.object_id,
          t2.delivery_date
FROM crm_sales_order t1
         LEFT JOIN crm_sales_order_product t2 ON
    t1.sales_order_code = t2.sales_order_code
         LEFT JOIN crm_cust_basic t3 ON
    t3.cust_code = t1.cust_code
WHERE t1.flag_deleted = 0
  AND t2.flag_deleted = 0
  AND t3.flag_deleted = 0
  and t3.cust_status=2 and t3.cust_type not in (0,1)
  AND t1.status not in (8)
  AND t1.sales_order_code='25040084A';
#   AND if(:admin,
#          1,
#          if(:cust_code_size>0, t3.cust_code in (:cust_code_arr), 1)
#       )
#   AND ((:deparment_code IS NULL OR :deparment_code = '') OR (t3.deparment_code = :deparment_code))
#  AND ((:region IS NULL OR :region = '') OR (t3.department_region = :region))
#   AND ((:cust_code IS NULL OR :cust_code = '') OR (t3.cust_code like concat('%', :cust_code, '%')))
#   AND ((:cust_name IS NULL OR :cust_name = '') OR (t3.cust_name LIKE concat('%', :cust_name, '%')))
#   AND ((:material_code IS NULL OR :material_code = '') OR (material_code like concat('%', :material_code, '%')))
#   AND ((:material_name IS NULL OR :material_name = '') OR (material_name LIKE concat('%', :material_name, '%')))
#   AND ((:contract_start_date IS NULL OR :contract_start_date = '') OR
#        (date_format(t1.create_time, '%Y-%m-%d') >= :contract_start_date))
#   AND ((:contract_end_date IS NULL OR :contract_end_date = '') OR
#        (date_format(t1.create_time, '%Y-%m-%d') <= :contract_end_date))
#   AND ((:factory_assigned IS NULL OR :factory_assigned = '') OR (t1.factory_assigned = :factory_assigned))
#   AND ((:sales_order_code IS NULL OR :sales_order_code = '') OR (t1.sales_order_code  LIKE concat('%',:sales_order_code,'%')))
#   AND if(:cust_manager_size>0, t3.cust_manager_code in (:cust_manager_arr), 1)
# AND if(:sales_assistant_size>0,t3.sales_assistant_code REGEXP :sales_assistant_str,1)
# ORDER BY t1.create_time desc
# LIMIT :page_size offset :offset
;
select * from crm_sales_order where sales_order_code='25030069A';
select product_quantity,order_quantity_after_split,unit_price_exclusive,exchange_rate from crm_sales_order_product csop where sales_order_code='25030069A';

-- 合计
SELECT '合计' AS                                                                 sale_company,
       round(sum(product_quantity), 2)                                        product_quantity,
       round(sum(amount_tax_inclusive ), 2)                 amount_tax_inclusive,
       round(sum(amount_exclusive_tax ), 2)                 amount_exclusive_tax,
       round(sum(settlement_currency_price_exclusive ), 4)  settlement_currency_price_exclusive,
       round(sum(settlement_currency_amount_exclusive ), 2) settlement_currency_amount_exclusive
FROM (
         SELECT
             '西安环球'                                                                                           sale_company,
             t3.deparment_code,
             t3.deparment_name,
             t3.department_region region,
             t3.cust_name,
             t3.cust_manager_code,
             t3.cust_manager_name,
             t3.cust_type,
             CASE
                 WHEN t3.cust_type = 0 THEN '新建客户'
                 WHEN t3.cust_type = 1 THEN '公海客户'
                 WHEN t3.cust_type = 2 THEN '合作客户'
                 WHEN t3.cust_type = 3 THEN '开发中客户'
                 WHEN t3.cust_type = 4 THEN '受限客户'
                 END                                                                                              cust_type_name,
             t1.sales_order_code,
             STR_TO_DATE(t1.create_time, '%Y-%m-%d')                                                              create_time,
             t2.main_class,
             t2.sub_class,
             t2.material_code,
             t2.material_name,
             t2.product_version,
             t2.order_quantity_after_split    product_quantity,
             round(IF(t2.unit_price_exclusive NOT REGEXP '^-?[0-9]+(\\.[0-9]+)?$', 0,
                      t2.unit_price_exclusive ),
                   4)                                                                                             unit_price_exclusive,
             round(IF(t2.amount_exclusive_tax NOT REGEXP '^-?[0-9]+(\\.[0-9]+)?$', 0,
                      t2.amount_exclusive_tax ),
                   2)                                                                                             amount_exclusive_tax,
             round(IF(t2.unit_price NOT REGEXP '^-?[0-9]+(\\.[0-9]+)?$', 0, t2.unit_price ), 4) unit_price,
             round(IF(t2.amount_tax_inclusive NOT REGEXP '^-?[0-9]+(\\.[0-9]+)?$', 0,
                      t2.amount_tax_inclusive ),
                   2)                                                                                             amount_tax_inclusive,
             t2.settlement_currency,
             round(IF(t2.settlement_currency_price_exclusive NOT REGEXP '^-?[0-9]+(\\.[0-9]+)?$', 0,
                      t2.settlement_currency_price_exclusive ),
                   4)                                                                                             settlement_currency_price_exclusive,
             round(IF(t2.settlement_currency_amount_exclusive NOT REGEXP '^-?[0-9]+(\\.[0-9]+)?$', 0,
                      t2.settlement_currency_amount_exclusive ),
                   2)                                                                                             settlement_currency_amount_exclusive,
             t1.factory_assigned,
             t1.quotation_code,
             t1.status,
             case
                 when t1.status=10 then '已删除'
                 when t1.status=8 then '已取消'
                 when t1.status=7 then '已关闭'
                 when t1.status=6 then '已开票'
                 when (select count(*)
                       from invoice_application ia
                       where ia.status IN ('0', '3', '4')
                         and ia.flag_deleted = 0
                         and ia.split_order_no like concat('%', t1.sales_order_code, '%')) > 0
                     then '已发货未开票'
                 when t1.status=0 then '已拆分'
                 when t1.status=1 then '已下达'
                 when t1.status=2 then '已排产'
                 when t1.status=3 then '已领料'
                 when t1.status=4 then '生产中'
                 when t1.status=5 then '已入库'
                 else '其他'
                 end status_desc
         FROM crm_sales_order t1
                  LEFT JOIN crm_sales_order_product t2 ON
             t1.sales_order_code = t2.sales_order_code
                  LEFT JOIN crm_cust_basic t3 ON
             t3.cust_code = t1.cust_code
         WHERE t1.flag_deleted = 0
           AND t2.flag_deleted = 0
           AND t3.flag_deleted = 0
           and t3.cust_status=2 and t3.cust_type not in (0,1)
           AND if(:admin,
                  1,
                  if(:cust_code_size>0, t3.cust_code in (:cust_code_arr), 1)
               )
           AND ((:deparment_code IS NULL OR :deparment_code = '') OR (t3.deparment_code = :deparment_code))
  AND ((:region IS NULL OR :region = '') OR (t3.department_region  = :region))
  AND ((:cust_code IS NULL OR :cust_code = '') OR (t3.cust_code like concat('%', :cust_code, '%')))
  AND ((:cust_name IS NULL OR :cust_name = '') OR (t3.cust_name LIKE concat('%', :cust_name, '%')))
  AND ((:material_code IS NULL OR :material_code = '') OR (material_code like concat('%', :material_code, '%')))
  AND ((:material_name IS NULL OR :material_name = '') OR (material_name LIKE concat('%', :material_name, '%')))
  AND ((:contract_start_date IS NULL OR :contract_start_date = '') OR
       (date_format(t1.create_time, '%Y-%m-%d') >= :contract_start_date))
  AND ((:contract_end_date IS NULL OR :contract_end_date = '') OR
       (date_format(t1.create_time, '%Y-%m-%d') <= :contract_end_date))
  AND ((:factory_assigned IS NULL OR :factory_assigned = '') OR (t1.factory_assigned = :factory_assigned))
  AND ((:sales_order_code IS NULL OR :sales_order_code = '') OR (t1.sales_order_code  LIKE concat('%',:sales_order_code,'%')))
  AND t1.status not in (8)
ORDER BY t1.id ASC
     )temp;
