-- 过程检验ETL
select update_month,process_code,
       process_name,
       max(first_inspect) first_inspect,
       max(first_inspect_fail) first_inspect_fail,
       max(first_inspect_deviation) first_inspect_deviation,
       max(first_inspect_pass_rate) first_inspect_pass_rate,
       max(avg_first_inspect) avg_first_inspect,
       0 patrol_inspect,
       0 patrol_fail,
       0 patrol_deviation,
       0 patrol_pass_rate,
       0 avg_patrol_inspect
from
(select date_format(ifnull(qc_create_time, t1.create_time), '%Y-%m')                     update_month,
       t1.production_processes                                                          process_code,
       t2.process_setting_name                                                          process_name,
       count(distinct production_batch_number)  first_inspect,
        0  first_inspect_fail,
        0 first_inspect_deviation,
        0 first_inspect_pass_rate,
        0 avg_first_inspect
from cockpit.ods_process_inspection t1
         left join cockpit.ods_qms_process_template t2 on t1.production_processes = t2.process_setting_code
where t1.flag_deleted = 0
  and t2.flag_deleted = 0
  and t1.inspection_type='jylx_3'
group by update_month, process_code
union
select date_format(ifnull(qc_create_time, t1.create_time), '%Y-%m')                     update_month,
       t1.production_processes                                                          process_code,
       t2.process_setting_name                                                          process_name,
       0  first_inspect,
       count(distinct production_batch_number)  first_inspect_fail,
       0 first_inspect_deviation,
       0 first_inspect_pass_rate,
       0 avg_first_inspect
from cockpit.ods_process_inspection t1
         left join cockpit.ods_qms_process_template t2 on t1.production_processes = t2.process_setting_code
where t1.flag_deleted = 0
  and t2.flag_deleted = 0
  and t1.inspection_type='jylx_3'
and t1.result=0
group by update_month, process_code
union
 select date_format(ifnull(qc_create_time, t1.create_time), '%Y-%m')                     update_month,
        t1.production_processes                                                          process_code,
        t2.process_setting_name                                                          process_name,
        0  first_inspect,
        0 first_inspect_fail,
        count(distinct t1.production_batch_number)  first_inspect_deviation,
        0 first_inspect_pass_rate,
        0 avg_first_inspect
 from cockpit.ods_process_inspection t1
          left join cockpit.ods_qms_process_template t2 on t1.production_processes = t2.process_setting_code
          left join cockpit.ods_abnormal_feedback t3 on t1.id=t3.mission_id
          left join cockpit.ods_qms_abnormal_feedback_treatment t4 on t4.abnormal_feedback_id=t3.id
 where t1.flag_deleted = 0
   and t2.flag_deleted = 0
   and ( t3.flag_deleted=0 OR t3.flag_deleted IS NULL)
   and (t4.flag_deleted=0 or t4.flag_deleted is null)
   and t1.inspection_type='jylx_3'
   and t1.result=0
   and t4.QC_being_measure='偏差使用'
 group by update_month, process_code
 union
 select date_format(ifnull(qc_create_time, t1.create_time), '%Y-%m')                     update_month,
        t1.production_processes                                                          process_code,
        t2.process_setting_name                                                          process_name,
        0  first_inspect,
        0 first_inspect_fail,
        0  first_inspect_deviation,
        Round(100*count(CASE when t1.result=1 then job_id end ) /count(job_id),2) first_inspect_pass_rate,
        0 avg_first_inspect
 from cockpit.ods_process_inspection t1
          left join cockpit.ods_qms_process_template t2 on t1.production_processes = t2.process_setting_code
 where t1.flag_deleted = 0
   and t2.flag_deleted = 0
   and t1.inspection_type='jylx_3'
 group by update_month, process_code
 union
 select date_format(ifnull(qc_create_time, t1.create_time), '%Y-%m')                     update_month,
        t1.production_processes                                                          process_code,
        t2.process_setting_name                                                          process_name,
        0  first_inspect,
        0 first_inspect_fail,
        0  first_inspect_deviation,
        0 first_inspect_pass_rate,
        round(count(distinct t1.production_batch_number)/
              (
                  count(distinct qc_create_by)+count(distinct sys_create_by)
                  )
            ,2) avg_first_inspect
 from cockpit.ods_process_inspection t1
          left join cockpit.ods_qms_process_template t2 on t1.production_processes = t2.process_setting_code
 where t1.flag_deleted = 0
   and t2.flag_deleted = 0
   and t1.inspection_type='jylx_3'
 group by update_month, process_code
)temp1
group by update_month, process_code
order by update_month,process_code;

-- 巡检
select update_month,
       process_code,
       process_name,
       0 first_inspect,
       0 first_inspect_fail,
       0 first_inspect_deviation,
       0 first_inspect_pass_rate,
       0 avg_first_inspect,
       max(patrol_inspect) patrol_inspect,
       max(patrol_fail) patrol_fail,
       max(patrol_deviation) patrol_deviation,
       max(patrol_pass_rate) patrol_pass_rate,
       max(avg_patrol_inspect) avg_patrol_inspect
from
    (select date_format(ifnull(qc_create_time, t1.create_time), '%Y-%m')                     update_month,
            t1.production_processes                                                          process_code,
            t2.process_setting_name                                                          process_name,
            count(distinct production_batch_number)  patrol_inspect,
            0  patrol_fail,
            0 patrol_deviation,
            0 patrol_pass_rate,
            0 avg_patrol_inspect
     from cockpit.ods_process_inspection t1
              left join cockpit.ods_qms_process_template t2 on t1.production_processes = t2.process_setting_code
     where t1.flag_deleted = 0
       and t2.flag_deleted = 0
       and t1.inspection_type='jylx_4'
     group by update_month, process_code
     union
     select date_format(ifnull(qc_create_time, t1.create_time), '%Y-%m')                     update_month,
            t1.production_processes                                                          process_code,
            t2.process_setting_name                                                          process_name,
            0  patrol_inspect,
            count(distinct production_batch_number)  patrol_fail,
            0 patrol_deviation,
            0 patrol_pass_rate,
            0 avg_patrol_inspect
     from cockpit.ods_process_inspection t1
              left join cockpit.ods_qms_process_template t2 on t1.production_processes = t2.process_setting_code
     where t1.flag_deleted = 0
       and t2.flag_deleted = 0
       and t1.inspection_type='jylx_4'
       and t1.result=0
     group by update_month, process_code
     union
     select date_format(ifnull(qc_create_time, t1.create_time), '%Y-%m')                     update_month,
            t1.production_processes                                                          process_code,
            t2.process_setting_name                                                          process_name,
            0  patrol_inspect,
            0 patrol_fail,
            count(distinct t1.production_batch_number)  patrol_deviation,
            0 patrol_pass_rate,
            0 avg_patrol_inspect
     from cockpit.ods_process_inspection t1
              left join cockpit.ods_qms_process_template t2 on t1.production_processes = t2.process_setting_code
              left join cockpit.ods_abnormal_feedback t3 on t1.id=t3.mission_id
              left join cockpit.ods_qms_abnormal_feedback_treatment t4 on t4.abnormal_feedback_id=t3.id
     where t1.flag_deleted = 0
       and t2.flag_deleted = 0
       and ( t3.flag_deleted=0 OR t3.flag_deleted IS NULL)
       and (t4.flag_deleted=0 or t4.flag_deleted is null)
       and t1.inspection_type='jylx_4'
       and t1.result=0
       and t4.QC_being_measure='偏差使用'
     group by update_month, process_code
     union
     select date_format(ifnull(qc_create_time, t1.create_time), '%Y-%m')                     update_month,
            t1.production_processes                                                          process_code,
            t2.process_setting_name                                                          process_name,
            0  patrol_inspect,
            0 patrol_fail,
            0  patrol_deviation,
            Round(100*count(CASE when t1.result=1 then job_id end ) /count(job_id),2) patrol_pass_rate,
            0 avg_patrol_inspect
     from cockpit.ods_process_inspection t1
              left join cockpit.ods_qms_process_template t2 on t1.production_processes = t2.process_setting_code
     where t1.flag_deleted = 0
       and t2.flag_deleted = 0
       and t1.inspection_type='jylx_4'
     group by update_month, process_code
     union
     select date_format(ifnull(qc_create_time, t1.create_time), '%Y-%m')                     update_month,
            t1.production_processes                                                          process_code,
            t2.process_setting_name                                                          process_name,
            0  patrol_inspect,
            0 patrol_fail,
            0  patrol_deviation,
            0 patrol_pass_rate,
            round(count(distinct t1.production_batch_number)/
                  (
                      count(distinct qc_create_by)+count(distinct sys_create_by)
                      )
                ,2) avg_patrol_inspect
     from cockpit.ods_process_inspection t1
              left join cockpit.ods_qms_process_template t2 on t1.production_processes = t2.process_setting_code
     where t1.flag_deleted = 0
       and t2.flag_deleted = 0
       and t1.inspection_type='jylx_4'
     group by update_month, process_code
    )temp2
group by update_month, process_code
order by update_month,process_code;
;


