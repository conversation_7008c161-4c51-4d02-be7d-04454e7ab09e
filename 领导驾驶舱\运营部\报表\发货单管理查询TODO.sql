select distinct a.id,
                a.shipments_code,
                a.shipments_type,
                date(a.production_date) as production_date,
                a.shipments_planner,
                a.shipments_department,
                b.*,
                c.*,
                CASE
                    WHEN b.is_finished_product = 1 THEN '销售订单'
                    ELSE (SELECT CASE
                                     WHEN d.source_type = 0 THEN '打样订单'
                                     WHEN d.source_type = 1 THEN '销售订单'
                                     ELSE '未知类型'
                                     END
                          FROM pm_job_order d
                          WHERE b.production_batch_number = d.production_batch_number
                          LIMIT 1)
                    END
                                        AS source_type,
                stowage_no,
                CASE
                    WHEN d.bill_source = 1 THEN '是'
                    ELSE '否'
                    END
                                        as virtual_type,
                d.num                   as virtual_num
from pm_shipments_inform a,
     pm_shipments_inform_detail b,
     (select l1.*
      from pm_flow_log l1,
           (select max(create_time) create_time, flow_id
            from pm_flow_log
            where flow_name = '发货通知单管理'
            group by flow_id) l2
      where l1.create_time = l2.create_time
        and l1.flow_id = l2.flow_id
        and l1.flow_name = '发货通知单管理') c,
     (select st.*, sb.bill_source
      from (select t.*
            from pm_stowage_detail t,
                 pm_stowage t1
            where t.parent_id = t1.id
              and t1.status_code != 4
              and t.flag_deleted = 0
              and 1 = (case
                           when ifnull(:stowage_no, '') = '' then 1
                           when ifnull(:stowage_no, '') <> '' and t1.stowage_no like concat('%', :stowage_no, '%')
                               then 1 end)) st
               left join pm_stowage_bill sb on sb.stowage_id = st.parent_id
      where st.flag_deleted = 0
        and 1 = (case
                     when ifnull(:virtual_type, '') = '' then 1
                     when ifnull(:virtual_type, '') <> '' and sb.bill_source = :virtual_type then 1 end)) d
where a.id = b.parent_id
  and a.id = c.flow_id
  and d.shipments_code = a.shipments_code
  and b.production_batch_number = d.batch_no
  and 1 = (case
               when ifnull(:is_finished_product, '') = '' then 1
               when ifnull(:is_finished_product, '') <> '' and b.is_finished_product = :is_finished_product then 1 end)
  and 1 = (case
               when ifnull(:cust_name, '') = '' then 1
               when ifnull(:cust_name, '') <> '' and b.cust_name like concat('%', :cust_name, '%') then 1 end)
  and 1 = (case
               when ifnull(:product_number, '') = '' then 1
               when ifnull(:product_number, '') <> '' and b.product_number like concat('%', :product_number, '%')
                   then 1 end)
  and 1 = (case
               when ifnull(:region_code, '') = '' then 1
               when ifnull(:region_code, '') <> '' and b.region_code like concat('%', :region_code, '%') then 1 end)
  and 1 = (case
               when ifnull(:product_name, '') = '' then 1
               when ifnull(:product_name, '') <> '' and b.product_name like concat('%', :product_name, '%') then 1 end)
  and 1 = (case
               when ifnull(:delivery_address, '') = '' then 1
               when ifnull(:delivery_address, '') <> '' and b.delivery_address like concat('%', :delivery_address, '%')
                   then 1 end)
  and 1 = (case
               when ifnull(:consignee, '') = '' then 1
               when ifnull(:consignee, '') <> '' and b.consignee like concat('%', :consignee, '%') then 1 end)
  and 1 = (case
               when ifnull(:next_node_name, '') = '' then 1
               when ifnull(:next_node_name, '') <> '' and c.next_node_name like concat('%', :next_node_name, '%')
                   then 1 end)
  AND ((:starttime IS NULL OR :starttime = '') OR (date(b.delivery_date) BETWEEN :starttime AND :endtime))
  AND ((:p_starttime IS NULL OR :p_starttime = '') OR (date(b.plan_delivery_date) BETWEEN :p_starttime AND :p_endtime))
  and 1 = (case
               when ifnull(:production_batch_number, '') = '' then 1
               when ifnull(:production_batch_number, '') <> '' and
                    b.production_batch_number like concat('%', :production_batch_number, '%') then 1 end)
  and a.flag_deleted = 0
  and b.flag_deleted = 0
  and c.flag_deleted = 0
  and d.flag_deleted = 0
order by b.delivery_date desc
limit :page_no,:page_size
