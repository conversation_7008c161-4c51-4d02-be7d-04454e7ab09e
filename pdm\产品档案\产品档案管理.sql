-- 产品档案相关表
select id,mnemonic_code,main_class,sub_class,is_group_product,t1. *
from pdm_product_version t1 where material_code in ('ZDHY0003034'); -- 产品版本表

select id,mnemonic_code,main_class,sub_class,is_group_product,t1. *
from pdm_product_version t1 where mnemonic_code in ('ZDHY0003034'); -- 产品版本表

select mnemonic_code,main_class,sub_class,is_group_product
    from pdm_product_version where main_class=6694;
select id,material_code,standard_unit_cn,standard_unit from pdm_product_version ppv where material_code='SMDJ0000079'
                                                                     and product_version like concat('%','1','%');

select * from pdm_product_ban_assunit ppba where edition_code  like 'ZDSW000057-1%'; -- 产品版档案
select *
from pdm_edition pe where edition_code like '%ZDHY0003431-34-GV1%' ; -- 版档案
select ppb.mnemonic_code,ppb.*
from pdm_product_bom ppb where material_code='WLSW0000109' ; -- 产品BOM明细表
select *
from pdm_product_client ppc where product_code='ZDZY0000902' ; -- 客户料号
select *
from pdm_product_craft ppc  where parent_code='ZDSW000057-1'; -- 工艺路线
select pm.id,pm.category,flag_deleted, pm.mnemonic_code,pm.*
from pdm_material pm where material_code='WLSW0000109'; -- 物料表

select id, standard_unit, procure_unit, t1.*
from pdm_material t1
where mnemonic_code in ('ZDSW000058');
-- 查询物料
select id, t1.product_version, standard_unit, t1.procure_unit, t1.*
from pdm_product_version t1
where material_code = 'ZDSW0001144';
-- 查询bom
select product_version, t1.*
from pdm_product_bom t1
where product_code = 'ZDYY0003150-4';


select
    distinct
    pv.id,
    pv.material_code,
    pv.mnemonic_code,
    pv.material_name,
    pv.is_group_product,
    pv.bom_type,
    pv.main_class_cn,
    pv.sub_class_cn,
    pv.product_version,
    pv.status,
    pv.product_size,
    pv.create_by,
    pv.create_time,
    pv.update_by,
    pv.update_time
FROM pdm_product_version pv
         LEFT JOIN pdm_product_client pc ON pv.material_code = pc.product_code
         LEFT JOIN pdm_product_bom pb ON pb.product_code = pv.material_code and pb.product_version = pv.product_version
         LEFT JOIN pdm_product_craft ppc ON ppc.product_code = pv.material_code and ppc.product_version = pv.product_version
         LEFT JOIN pdm_upload_file pf ON pf.material_code = pv.material_code and pf.product_version = pv.product_version
where 1=1 and pv.flag_deleted = 0 and pv.status in(1,2,3,4)
#   and ((:material_code is null or :material_code = '' )  or (pv.material_code like concat ('%',:material_code,'%')))
#   and ((:material_name is null or :material_name = '' )  or (pv.material_name like concat ('%',:material_name,'%')))
#   and ((:mater_code is null or :mater_code = '' )  or (pb.material_code like concat ('%',:mater_code,'%')))
#   and ((:mater_name is null or :material_name = '' )  or (pb.material_name like concat ('%',:mater_name,'%')))
#   and ((:processname is null or :processname = '' )  or (ppc.craft_name like concat ('%',:processname,'%')))
#   and ((:is_group is null or :is_group = '' )  or (pv.is_group_product = :is_group))
#   and ((:status is null or :status = '' )  or (pv.status = :status))
#   and  ((:startcreatetime is null or :startcreatetime = '') or (pv.create_time >=:startcreatetime))
#   and  ((:endcreatetime is null or :endcreatetime = '') or (pv.create_time <=:endcreatetime))
#   and  ((:startupdatetime is null or :startupdatetime = '') or (pv.update_time >=:startupdatetime))
#   and  ((:endupdatetime is null or :endupdatetime = '') or (pv.update_time <=:endupdatetime))
#   and ((:bom_type is null or :bom_type = '' )  or (pv.bom_type = :bom_type))
#   and ((:product_version is null or :product_version = '' )  or (pv.product_version = :product_version))
#   and if(:main_class_size>0,pv.main_class in  (:main_class),1)
#   and if(:sub_class_size>0,pv.sub_class in (:sub_class),1)
#   and ((:mnemonic_code is null or :mnemonic_code = '') or (pv.mnemonic_code like concat ('%',:mnemonic_code,'%')))
#   and ((:client_name is null or :client_name = '') or (pc.client_name like concat ('%',:client_name,'%')))
#   and ((:product_size is null or :product_size = '') or (pv.product_size like concat ('%',:product_size,'%')))
#   and ((:paper_size is null or :paper_size = '') or (pb.component_size like concat ('%',:paper_size,'%') and categroy = '1'))
#   and case when :fileFlag = '1' then pf.file_id is not null else 1 end
order by pv.id desc
limit 0,10
;

SELECT
    pv.id,
    pv.material_code,
    pv.mnemonic_code,
    pv.material_name,
    pv.is_group_product,
    pv.bom_type,
    pv.main_class_cn,
    pv.sub_class_cn,
    pv.product_version,
    pv.status,
    pv.product_size,
    pv.create_by,
    pv.create_time,
    pv.update_by,
    pv.update_time
FROM pdm_product_version pv
WHERE pv.flag_deleted = 0
  AND pv.material_code is not null
  AND pv.status IN (1,2,3,4)
#   AND (:material_code IS NULL OR :material_code = '' OR pv.material_code LIKE CONCAT('%', :material_code, '%'))
#   AND (:material_name IS NULL OR :material_name = '' OR pv.material_name LIKE CONCAT('%', :material_name, '%'))
#   AND (:mnemonic_code IS NULL OR :mnemonic_code = '' OR pv.mnemonic_code LIKE CONCAT('%', :mnemonic_code, '%'))
#   AND (:is_group IS NULL OR :is_group = '' OR pv.is_group_product = :is_group)
#   AND (:bom_type IS NULL OR :bom_type = '' OR pv.bom_type = :bom_type)
#   AND (:product_version IS NULL OR :product_version = '' OR pv.product_version = :product_version)
#   AND (:product_size IS NULL OR :product_size = '' OR pv.product_size LIKE CONCAT('%', :product_size, '%'))
#   AND (:startcreatetime IS NULL OR :startcreatetime = '' OR pv.create_time >= :startcreatetime)
#   AND (:endcreatetime IS NULL OR :endcreatetime = '' OR pv.create_time <= :endcreatetime)
#   AND (:startupdatetime IS NULL OR :startupdatetime = '' OR pv.update_time >= :startupdatetime)
#   AND (:endupdatetime IS NULL OR :endupdatetime = '' OR pv.update_time <= :endupdatetime)
#   AND IF(:main_class_size > 0, pv.main_class IN (:main_class), 1)
#   AND IF(:sub_class_size > 0, pv.sub_class IN (:sub_class), 1)
  AND IF(:client_name IS NULL OR :client_name = '', 1,
         EXISTS (SELECT 1 FROM pdm_product_client pc
                 WHERE pc.product_code = pv.material_code
                   and pc.product_version = pv.product_version
                   AND pc.client_name LIKE CONCAT('%', :client_name, '%')))
#   AND IF(:mater_code IS NULL OR :mater_code = '', 1,
#          EXISTS (SELECT 1 FROM pdm_product_bom pb
#                  WHERE pb.product_code = pv.material_code
#                    AND pb.product_version = pv.product_version
#                    AND pb.material_code LIKE CONCAT('%', :mater_code, '%')))
#
#   AND IF(:mater_name IS NULL OR :mater_name = '', 1,
#          EXISTS (SELECT 1 FROM pdm_product_bom pb
#                  WHERE pb.product_code = pv.material_code
#                    AND pb.product_version = pv.product_version
#                    AND pb.material_name LIKE CONCAT('%', :mater_name, '%')))
#
#   AND IF(:paper_size IS NULL OR :paper_size = '', 1,
#          EXISTS (SELECT 1 FROM pdm_product_bom pb
#                  WHERE pb.product_code = pv.material_code
#                    AND pb.product_version = pv.product_version
#                    AND pb.component_size LIKE CONCAT('%', :paper_size, '%')
#                    AND pb.categroy = '1'))
#   AND IF(:processname IS NULL OR :processname = '', 1,
#          EXISTS (SELECT 1 FROM pdm_product_craft ppc
#                  WHERE ppc.product_code = pv.material_code
#                    AND ppc.product_version = pv.product_version
#                    AND ppc.craft_name LIKE CONCAT('%', :processname, '%')))
#
#   AND IF(:fileFlag = '1',
#          EXISTS (SELECT 1 FROM pdm_upload_file pf
#                  WHERE pf.material_code = pv.material_code
#                    AND pf.product_version = pv.product_version
#                    AND pf.file_id IS NOT NULL), 1)

ORDER BY pv.id DESC
LIMIT :pageNo, :pageSize
;

