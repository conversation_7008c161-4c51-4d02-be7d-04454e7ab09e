select id, product_version, is_group_product,standard_unit,standard_unit_cn, ppv.*
from pdm_product_version ppv
where material_code = 'ZDSW0000230';
select id, product_version, is_group_product,standard_unit,standard_unit_cn, ppv.*
from pdm_product_version ppv
where is_group_product is null or standard_unit = 9077;


-- 修改material_code,product_version,standard_unit
INSERT INTO pdm_product_bom (dosage_unit_unit_cn, packing_unit_cn, consume_unit_cn, standard_unit_cn, product_count,
                             bom_type, direct_material, indexs, pack_paste_method, pack_other_texture,
                             pack_background_color, pack_other_sizes, box_mark_content, packing_parameter, weight,
                             packing_unit, ink_formulation, consume_round_up, consume_unit, dosage_unit_unit, cut_size,
                             notes_on_cutting, slitting, cut_remark, pushing, is_off_side, consumption_rate,
                             dosage_unit, fixed_amount, product_version, product_code, parent_code, makeup_product,
                             remark, part_information, color_order_quotation, color_sequence, chromatic_degree,
                             standard_unit, spread_size, component_size, component_count, material_name, mnemonic_code,
                             material_code, categroy, flag_deleted, version, create_by, create_time, update_by,
                             update_time, domain_id, gram_weight_cn, mine_version, children_version, children_product)
VALUES (null, null, null, null, '1', null,
        null, null, null, null, null, null,
        null, null, null, null, null, null,
        null,null, null, null, null, null, null,
        null,null, null, null, '4.2', 'ZDSW0000230',
        '',null, null, null, null,null,
        '1/1', 6591, '1*1', null, '1',null,
        null, 'ZDSW0000230', '3', 0, null, null,null,
        null, null, null,null, null, null, null);

select * from pdm_product_bom pm where material_code='ZDSW0000230';
