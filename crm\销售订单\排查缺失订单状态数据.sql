-- 查询缺失的数据
select
    cso.id,
    cso.sales_order_code ,
    cso.csaleorderid
from h3chq_crmbusiness1704287359505.crm_sales_order cso
where csaleorderid is null and flag_deleted=0;


SELECT
    json_extract( mmo.`data`,'$.erp_production_order'),
    mmo.id,
    mmo.module_name,
    mmo.`data`,
    mmo.msg,
    mmo.state,
    CASE
        WHEN mmo.state = 0 THEN '未消费'
        WHEN mmo.state = 1 THEN '已消费'
        ELSE mmo.state
        END state_name,
    mmo.create_time
FROM
    hq_mdm_b.mdm_mom_afferent mmo
WHERE
    mmo.flag_deleted = 0
  AND mmo.module_name LIKE '%销售订单%'
  AND json_extract( mmo.`data`,'$.erp_production_order') in (
           '25030017A','25030366A','25030524A','25040051A'
     )
ORDER BY
    id DESC
;

