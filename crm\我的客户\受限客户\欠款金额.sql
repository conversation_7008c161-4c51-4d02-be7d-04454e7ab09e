select ifnull(sum(qkbbbye), 0) unpaid_money,
       ifnull(max(cast(qkts as signed)), 0)    unpaid_days
from crm_account_receivable_age
where cust_code = 'C000310'
  and flag_deleted = 0
  and date_format(create_time, '%Y-%m') =
      (select date_format(max(create_time), '%Y-%m') from crm_account_receivable_age where flag_deleted = 0)
;


select ifnull(sum(qkbbbye), 0) unpaid_money,
       ifnull(max(qkts), 0)    unpaid_days
from crm_account_receivable_age
where cust_code = 'C003614'
  and flag_deleted = 0
  and date_format(create_time, '%Y-%m') =
      (select date_format(max(create_time), '%Y-%m') from crm_account_receivable_age where flag_deleted = 0)
;
SELECT SUM(qkbbbye) AS unpaid_money, MAX(CAST(qkts AS SIGNED)) AS unpaid_days
FROM crm_account_receivable_age
WHERE cust_code = 'C00002411';
select *
from crm_account_receivable_age
where cust_code = 'C000024';
