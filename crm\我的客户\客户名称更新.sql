-- 查询
select t1.id, t1.cust_manager_code,t1.cust_manager_name from
    h3chq_crmbusiness1704287359505.crm_cust_basic t1
    join h3c_biip_usercenter.sys_user t2 on t1.cust_manager_code=t2.user_id and t2.del_flag=0
where t1.cust_manager_name ='' or t1.cust_manager_name is null;
-- 执行更新
update h3chq_crmbusiness1704287359505.crm_cust_basic t1
    join h3c_biip_usercenter.sys_user t2 on t1.cust_manager_code=t2.user_id and t2.del_flag=0
set t1.cust_manager_name=t2.name
where t1.cust_manager_name ='' or t1.cust_manager_name is null;

-- 销售订单里更新
select t1.id, t1.cust_manager_code,t1.cust_manager_name from
    h3chq_crmbusiness1704287359505.crm_sales_order  t1
        join h3c_biip_usercenter.sys_user t2 on t1.cust_manager_code=t2.user_id and t2.del_flag=0
where t1.cust_manager_name !='';

update h3chq_crmbusiness1704287359505.crm_sales_order t1
    join h3c_biip_usercenter.sys_user t2 on t1.cust_manager_code=t2.user_id and t2.del_flag=0
set t1.cust_manager_name=t2.name
where t1.cust_manager_name !='';
