-- 开发计划查询
select ccp.id,
       ccp.development_plan_code,
       ccp.development_plan_status,
       case
           ccp.development_plan_status
           when 0 then '草稿'
           when 1 then '审核中'
           when 2 then '审核不通过'
           when 3 then '执行中'
           when 4 then '完成'
           when 5 then '超期'
           when 6 then '关闭'
           when 7 then '节点超期'
           end                                          as development_plan_status_desc,
       ccp.development_plan_result,
       ccp.development_plan_result                      as development_plan_result_desc,
       ccp.failed_development_projects,
       ccp.successful_development_projects,
       ccp.cust_code,
       ccp.cust_name,
       ccp.industry,
       ccp.current_stage,
       ccp.sales_assistant_name,
       ccp.cust_manager_name,
       ccp.sales_organization_name,
       ccp.customer_demand,
       ccp.customer_pain_points,
       ccp.competitor_advantages,
       ccp.counterattack_strategy,
       ccp.show_update_by,
       ccp.show_create_by,
       ccp.create_time,
       ccp.update_time,
       ccp.status,
       CONCAT_WS(
               '-',
               CONCAT(
                       year(ccp.development_start_date), '年',
                       month(ccp.development_start_date), '月'
               ),
               CONCAT(
                       month(ccp.development_end_date), '月'
               )
       )                                                as date_range,
       CONCAT_WS(
               '至',
               CONCAT(
                       year(ccp.development_start_date), '年',
                       month(ccp.development_start_date), '月',
                       day(ccp.development_start_date), '日'
               ),
               CONCAT(
                       year(ccp.development_start_date), '年',
                       month(ccp.development_start_date), '月',
                       day(ccp.development_start_date), '日'
               )
       )                                                as date_ranges,
       ifnull((select count(1)
               from crm_cust_dp_template ccdt
               where ccdt.flag_deleted = 0
                 and ccdt.development_plan_code = ccp.development_plan_code
                 and ccdt.phase_name = ccp.current_stage
                 and ccdt.is_execute = 1
               group by ccdt.development_plan_code), 0) as executeNo
from crm_cust_plan ccp;

-- 计划模板查询客户助记码
select cust_mnemonic_code
from crm_cust_dp_template
where phase_name = :phase_name
  and development_plan_code = :development_plan_code
  and flag_deleted = 0
limit 1
;

-- 查询开发计划
select *
from crm_cust_plan ccp
where development_plan_code = 'KF20250120002';
select *
from crm_cust_plan ccp
where cust_mnemonic_code = 'CC005023';

select *
from crm_cust_dp_template
where development_plan_code = 'KF20250115005';
select *
from crm_cust_dp_template
where cust_mnemonic_code = 'CC004997';

select *
from crm_cust_dp_template as ccdt
         left join crm_cust_plan as ccp on ccdt.development_plan_code = ccp.development_plan_code
where ccdt.cust_mnemonic_code = :cust_code
  and ((:development_plan_code is null or :development_plan_code = '') or
       (ccdt.development_plan_code like concat("%", :development_plan_code, "%")))
#   and ccdt.is_execute is null
order by ccdt.create_time desc
limit 1;


select id,cust_code,cust_manager_code,sales_assistant_code,ccp.* from crm_cust_plan ccp where development_plan_code='KF20250226001';
