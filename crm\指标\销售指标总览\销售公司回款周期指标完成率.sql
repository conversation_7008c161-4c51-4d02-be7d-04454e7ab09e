-- ETL建表
drop table if exists dws_company_profit_month;
create table dws_company_profit_month
(
    id                int auto_increment primary key comment '自增主键'
        primary key,
    update_year       varchar(10)    null comment '指标年度',
    update_month      varchar(10)    null comment '指标月份',
    update_time       varchar(10)    null comment '指标时间',
    cumulative_amount decimal(20, 2) null comment '累计金额'
) comment '销售公司毛利累计';
select * from dws_company_profit_month;


select year(now())-1 year,
       1 quater,
       ifnull(sum(case when metric_year=year(now())-1 then ifnull(payment_cycle_q1,0) end),0) payment_cycle
from cockpit.dws_payment_cycle_metric t1
where metric_year =year(now())-1
;


select year(now())-1 year,
       1 quater,
       ifnull(sum(case when metric_year=year(now())-1 then ifnull(payment_cycle_q1,0) end),0) payment_cycle
from cockpit.dws_payment_cycle_metric t1
where metric_year =year(now())-1
UNION ALL
select year(now())-1 year,
       2 quater,
       ifnull(sum(case when metric_year=year(now())-1 then ifnull(payment_cycle_q2,0) end),0) payment_cycle
from cockpit.dws_payment_cycle_metric t1
where metric_year =year(now())-1
UNION ALL
select year(now())-1 year,
       3 quater,
       ifnull(sum(case when metric_year=year(now())-1 then ifnull(payment_cycle_q3,0) end),0) payment_cycle
from cockpit.dws_payment_cycle_metric t1
where metric_year =year(now())-1
UNION ALL
select year(now())-1 year,
       4 quater,
       ifnull(sum(case when metric_year=year(now())-1 then ifnull(payment_cycle_q4,0) end),0) payment_cycle
from cockpit.dws_payment_cycle_metric t1
where metric_year =year(now())-1
UNION ALL
select year(now()) `year`,
       1 quater,
       ifnull(sum(case when metric_year=year(now()) then ifnull(payment_cycle_q1,0) end),0) payment_cycle
from cockpit.dws_payment_cycle_metric t1
where metric_year =year(now())
UNION ALL
select year(now()) `year`,
       2 quater,
       ifnull(sum(case when metric_year=year(now()) then ifnull(payment_cycle_q2,0) end),0) payment_cycle
from cockpit.dws_payment_cycle_metric t1
where metric_year =year(now())
UNION ALL
select year(now()) `year`,
       3 quater,
       ifnull(sum(case when metric_year=year(now()) then ifnull(payment_cycle_q3,0) end),0) payment_cycle
from cockpit.dws_payment_cycle_metric t1
where metric_year =year(now())
UNION ALL
select year(now()) `year`,
       4 quater,
       ifnull(sum(case when metric_year=year(now()) then ifnull(payment_cycle_q4,0) end),0) payment_cycle
from cockpit.dws_payment_cycle_metric t1
where metric_year =year(now())
ORDER BY year, quater
;




