-- 农务系统数据
drop table IF EXISTS ods_farm_metric;
CREATE TABLE ods_farm_metric
(
    id                             BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    season                         VARCHAR(10)    NOT NULL COMMENT '榨季',
    factory_code                   VARCHAR(10)    NOT NULL COMMENT '工厂代码',
    entry_date                     varchar(20)    NOT NULL COMMENT '进厂时间',
    cane_quantity                  DECIMAL(16, 8) NOT NULL COMMENT '甘蔗数量',
    season_cumulative_weight       DECIMAL(16, 8) NOT NULL COMMENT '当日榨季累计已过磅甘蔗量',
    last_season_same_period_weight DECIMAL(16, 8) NOT NULL COMMENT '上榨季同期已过磅甘蔗量',
    manual_cut_quantity            DECIMAL(16, 8) NOT NULL COMMENT '人工砍蔗甘蔗数量',
    machine_cut_quantity           DECIMAL(16, 8) NOT NULL COMMENT '机收蔗甘蔗数量',
    season_cumulative_manual_cut   DECIMAL(16, 8) NOT NULL COMMENT '当日榨季累计人工砍蔗数量',
    season_cumulative_machine_cut  DECIMAL(16, 8) NOT NULL COMMENT '当日榨季累计机收蔗数量',
    machine_cut_percentage         VARCHAR(10)    NOT NULL COMMENT '机收蔗榨季累计数量占比',
    cane_variety_code              VARCHAR(50)    NOT NULL COMMENT '甘蔗品种代码',
    cane_variety                   VARCHAR(50)    NOT NULL COMMENT '甘蔗品种名称',
    variety_daily_quantity         DECIMAL(16, 8) NOT NULL COMMENT '各甘蔗品种当日数量',
    variety_season_quantity        DECIMAL(16, 8) NOT NULL COMMENT '各甘蔗品种榨季累计数量',
    waiting_transport_quantity     DECIMAL(16, 8) NOT NULL COMMENT '当日待运已过磅甘蔗量',
    outside_factory_quantity       DECIMAL(16, 8) NOT NULL COMMENT '当日厂外已过磅甘蔗量',
    in_transit_quantity            DECIMAL(16, 8) NOT NULL COMMENT '当日在途已过磅甘蔗量',
    INDEX idx_factory_date (factory_code, entry_date) COMMENT '工厂日期复合索引',
    INDEX idx_season (season) COMMENT '榨季索引',
    INDEX idx_variety (cane_variety) COMMENT '甘蔗品种索引',
    INDEX idx_variety_code (cane_variety_code) COMMENT '甘蔗品种代码索引'
) COMMENT ='农务系统接口';

select t0.cane_variety,
       t0.cane_variety_code,
       t1.variety_daily_quantity  variety_daily_quantity,
       t1.variety_season_quantity variety_season_quantity,
       t2.variety_daily_sugar     variety_daily_sugar,
       t2.variety_season_sugar    variety_season_sugar
from (select cane_variety_code, cane_variety
      from ods_farm_metric
      group by cane_variety_code) t0
         left join

     (select cane_variety_code,
             cane_variety,
             round(variety_daily_quantity)  variety_daily_quantity,
             round(variety_season_quantity) variety_season_quantity
      from ods_farm_metric
      where entry_date=current_date
     ) t1 on t0.cane_variety_code = t1.cane_variety_code
         left join (select variety_code,
                           variety_name,
                           round(variety_daily_sugar, 2)  variety_daily_sugar,
                           round(variety_season_sugar, 2) variety_season_sugar
                    from ods_cqms_metric ofm
                    where entry_date=current_date
) t2 on t1.cane_variety_code = t2.variety_code
order by t1.variety_daily_quantity desc
limit 5
;


SELECT id, varieties,
       today_quantity,
       accumulated_quantity,
       format(today_sucrose,2) today_sucrose,
       format(accumulated_sucrose,2) accumulated_sucrose
FROM cane_varieties
ORDER BY today_quantity DESC
LIMIT 5;
select round(
               (select caneflow_accum_season from extraction_volume limit 1) /
               (select employee_number from employee_number limit 1)
       ) as average_num
;
select caneflow_accum_season from extraction_volume limit 1;
