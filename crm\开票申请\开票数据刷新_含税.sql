select  invoice_include_price
from invoice_application_detail where  status in (0,3,4) and tax_inclusive='含税' and apply_no='VR202503312807';
select cast(1.275*4223 as decimal(18,2));
-- jsb_unit_price_include_tax
update invoice_application_detail set jsb_unit_price_include_tax=cast(invoice_include_price as  decimal(18,6))
where  status in (0,3,4)  and tax_inclusive='含税';
-- jsb_amount_include_tax
update invoice_application_detail set jsb_amount_include_tax=cast(jsb_unit_price_include_tax*apply_invoice_quantity as decimal(18,2))
where  status in (0,3,4)  and tax_inclusive='含税';
--  amount_tax_inclusive
update invoice_application_detail set amount_tax_inclusive=cast(jsb_amount_include_tax*exchange_rate as decimal(18,2))
where  status in (0,3,4)  and tax_inclusive='含税';
-- price_including_tax
update invoice_application_detail set price_including_tax=cast(amount_tax_inclusive/apply_invoice_quantity as decimal(18,6))
where  status in (0,3,4)  and tax_inclusive='含税';
-- amount_exclusive_tax
update invoice_application_detail set amount_exclusive_tax=cast(amount_tax_inclusive/(1+tax_rate/100) as decimal(18,2))
where  status in (0,3,4)  and tax_inclusive='含税';
-- jsb_amount_exclude_tax
update invoice_application_detail set jsb_amount_exclude_tax=cast(amount_exclusive_tax/exchange_rate as decimal(18,2))
where  status in (0,3,4)  and tax_inclusive='含税';
-- jsb_unit_price_exclude_tax
update invoice_application_detail set jsb_unit_price_exclude_tax=cast(jsb_amount_exclude_tax/apply_invoice_quantity as decimal(18,6))
where  status in (0,3,4) and tax_inclusive='含税';
-- unit_price_exclusive
update invoice_application_detail set unit_price_exclusive=cast(amount_exclusive_tax/apply_invoice_quantity as decimal(18,6))
where  status in (0,3,4)  and tax_inclusive='含税';
-- tax_diff
update invoice_application_detail set tax_diff=cast(amount_tax_inclusive-amount_exclusive_tax as decimal(18,2))
where  status in (0,3,4)  and tax_inclusive='含税';
-- price_diff
update invoice_application_detail set price_diff=cast(invoice_include_price-origin_unit_price_include_tax as decimal(18,6))
where  status in (0,3,4)  and tax_inclusive='含税';



