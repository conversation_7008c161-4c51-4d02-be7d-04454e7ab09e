select last_month_cost_price,selling_price,pm.conversion_rate,ROUND(last_month_cost_price *
                                                      CASE
                                                          WHEN pm.conversion_rate LIKE '%/%'
                                                              THEN (
                                                              CAST(TRIM(SUBSTRING_INDEX(pm.conversion_rate, '/', 1)) AS DECIMAL(10,4)) /
                                                              CAST(TRIM(SUBSTRING_INDEX(pm.conversion_rate, '/', -1)) AS DECIMAL(10,4))
                                                              )
                                                          ELSE CAST(pm.conversion_rate AS DECIMAL(10,4))
                                                          END, 4),
    cpm.* from crm_price_manager cpm  join
 h3chq_pdmbusiness1704287270935.pdm_material pm on cpm.material_code=pm.material_code
where cpm.main_class like '%卷筒%' and last_month_cost_price !=0 and last_month_cost_price is not null and last_month_cost_price !=''
and cpm.material_code='1010101010000396';


UPDATE crm_price_manager cpm
    JOIN h3chq_pdmbusiness1704287270935.pdm_material pm
    ON cpm.material_code = pm.material_code
SET last_month_cost_price = ROUND(last_month_cost_price *
                                  CASE
                                      WHEN pm.conversion_rate LIKE '%/%'
                                          THEN (
                                          CAST(TRIM(SUBSTRING_INDEX(pm.conversion_rate, '/', 1)) AS DECIMAL(10,4)) /
                                          CAST(TRIM(SUBSTRING_INDEX(pm.conversion_rate, '/', -1)) AS DECIMAL(10,4))
                                          )
                                      ELSE CAST(pm.conversion_rate AS DECIMAL(10,4))
                                      END, 4)

WHERE cpm.main_class LIKE '%卷筒%'
  AND last_month_cost_price != 0
  AND last_month_cost_price IS NOT NULL
  AND last_month_cost_price != ''
;
UPDATE crm_price_manager cpm
    JOIN h3chq_pdmbusiness1704287270935.pdm_material pm
    ON cpm.material_code = pm.material_code
SET selling_price=last_month_cost_price

WHERE cpm.main_class LIKE '%卷筒%'
  AND last_month_cost_price != 0
  AND last_month_cost_price IS NOT NULL
  AND last_month_cost_price != '';

