-- 库存金额和天数
-- 查下客户的库存金额和库存天数
select
    ifnull(sum(t1.nonhandnum*t6.unit_price_exclusive),0) inventory_money,
    ifnull(datediff(current_date,max(t1.dinbounddate)),0) inventory_days
from h3chq_crmbusiness1704287359505.ods_bip_inventory t1
         left join hq_pm_b.pm_job_order t2
                   on t1.vbatchcode = t2.production_batch_number COLLATE utf8mb4_general_ci
                       and t1.materialcode = t2.material_code COLLATE utf8mb4_general_ci
                       and t2.flag_deleted = 0
         left join hq_pm_b.pm_sale_order t3 on t3.order_number = t2.order_code and t3.flag_deleted = 0
         left join hq_pm_b.pm_order_product t4 on t4.sales_number = t2.order_code
    and t4.id = t2.source_detail_id
    and t4.flag_deleted = 0
         left join h3chq_crmbusiness1704287359505.crm_sales_order t5 on t3.erp_production_order = t5.sales_order_code
    and t5.csaleorderid = t3.bip_main_no
    and t5.flag_deleted = 0
         left join h3chq_crmbusiness1704287359505.crm_sales_order_product t6 on t6.id = t4.crm_order_num
    and t3.erp_production_order = t6.sales_order_code
    and t6.flag_deleted = 0
where t1.flag_deleted = 0
  and t4.crm_order_num is not null
  and t5.contract_management_code is not null
  and t5.cust_code='C004736'
;
