CREATE TABLE `crm_paper_forecast`
(
    `paper_forecast_code`  varchar(255) DEFAULT NULL COMMENT '纸张预测编码',
    `forecast_dimension`   varchar(255) DEFAULT NULL COMMENT '预测维度',
    `year_month`           varchar(255) DEFAULT NULL COMMENT '年/月份',
    `cust_manager_code`    varchar(255) DEFAULT NULL COMMENT '客户负责人编码',
    `cust_manager_name`    varchar(255) DEFAULT NULL COMMENT '客户负责人名称',
    `sales_assistant_code` varchar(255) DEFAULT NULL COMMENT '销售助理编码',
    `sales_assistant_name` varchar(255) DEFAULT NULL COMMENT '销售助理名称',
    `dept`                 varchar(255) DEFAULT NULL COMMENT '部门',
    `status`               varchar(5)   DEFAULT NULL COMMENT '状态(0:待审批,1:审批中,2:生效,3:驳回)',
    `approver`             varchar(255) DEFAULT NULL COMMENT '审批人',
    `approver_time`        datetime     DEFAULT NULL COMMENT '审批时间',
    `approver_opinion`     varchar(255) DEFAULT NULL COMMENT '审批意见',
    `remark`               varchar(255) DEFAULT NULL COMMENT '备注',

    `update_time`          datetime     DEFAULT NULL COMMENT '修改时间',
    `update_by`            varchar(50)  DEFAULT NULL COMMENT '修改人',
    `create_time`          datetime     DEFAULT NULL COMMENT '创建时间',
    `create_by`            varchar(50)  DEFAULT NULL COMMENT '创建人',
    `version`              varchar(20)  DEFAULT NULL COMMENT '版本(纸张预测)',
    `flag_deleted`         int          DEFAULT '0' COMMENT '是否删除',
    `id`                   int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `domain_id`            varchar(50)  DEFAULT NULL COMMENT '域id',
    PRIMARY KEY (`id`)
) COMMENT = '纸张预测';
select * from crm_paper_forecast;


CREATE TABLE `crm_paper_forecast_data`
(
    `year_december_review`           varchar(50)  DEFAULT NULL COMMENT '十二月回顾',
    `year_december_forecast`         varchar(50)  DEFAULT NULL COMMENT '十二月预测',
    `year_november_review`           varchar(50)  DEFAULT NULL COMMENT '十一月回顾',
    `year_november_forecast`         varchar(50)  DEFAULT NULL COMMENT '十一月预测',
    `year_october_review`            varchar(50)  DEFAULT NULL COMMENT '十月回顾',
    `year_october_forecast`          varchar(50)  DEFAULT NULL COMMENT '十月预测',
    `year_september_review`          varchar(50)  DEFAULT NULL COMMENT '九月回顾',
    `year_september_forecast`        varchar(50)  DEFAULT NULL COMMENT '九月预测',
    `year_august_review`             varchar(50)  DEFAULT NULL COMMENT '八月回顾',
    `year_august_forecast`           varchar(50)  DEFAULT NULL COMMENT '八月预测',
    `year_july_review`               varchar(50)  DEFAULT NULL COMMENT '七月回顾',
    `year_july_forecast`             varchar(50)  DEFAULT NULL COMMENT '七月预测',
    `year_june_review`               varchar(50)  DEFAULT NULL COMMENT '六月回顾',
    `year_june_forecast`             varchar(50)  DEFAULT NULL COMMENT '六月预测',
    `year_may_review`                varchar(50)  DEFAULT NULL COMMENT '五月回顾',
    `year_may_forecast`              varchar(50)  DEFAULT NULL COMMENT '五月预测',
    `year_april_review`              varchar(50)  DEFAULT NULL COMMENT '四月回顾',
    `year_april_forecast`            varchar(50)  DEFAULT NULL COMMENT '四月预测',
    `year_march_review`              varchar(50)  DEFAULT NULL COMMENT '三月回顾',
    `year_march_forecast`            varchar(50)  DEFAULT NULL COMMENT '三月预测',
    `year_february_review`           varchar(50)  DEFAULT NULL COMMENT '二月回顾',
    `year_february_forecast`         varchar(50)  DEFAULT NULL COMMENT '二月预测',
    `year_january_review`            varchar(50)  DEFAULT NULL COMMENT '一月回顾',
    `year_january_forecast`          varchar(50)  DEFAULT NULL COMMENT '一月预测',
    `last_year_december_production`  varchar(50)  DEFAULT NULL COMMENT '上年十二月生产',
    `last_year_november_production`  varchar(50)  DEFAULT NULL COMMENT '上年十一月生产',
    `last_year_october_production`   varchar(50)  DEFAULT NULL COMMENT '上年十月生产',
    `last_year_september_production` varchar(50)  DEFAULT NULL COMMENT '上年九月生产',
    `last_year_august_production`    varchar(50)  DEFAULT NULL COMMENT '上年八月生产',
    `last_year_july_production`      varchar(50)  DEFAULT NULL COMMENT '上年七月生产',
    `last_year_june_production`      varchar(50)  DEFAULT NULL COMMENT '上年六月生产',
    `last_year_may_production`       varchar(50)  DEFAULT NULL COMMENT '上年五月生产',
    `last_year_april_production`     varchar(50)  DEFAULT NULL COMMENT '上年四月生产',
    `last_year_march_production`     varchar(50)  DEFAULT NULL COMMENT '上年三月生产',
    `last_year_february_production`  varchar(50)  DEFAULT NULL COMMENT '上年二月生产',
    `last_year_january_production`   varchar(50)  DEFAULT NULL COMMENT '上年一月生产',

    `production_factory`             varchar(50)  DEFAULT NULL COMMENT '生产工厂(1:凌峰,2:天津)',
     work_code                          varchar(50)  DEFAULT NULL COMMENT '工号',
    `responsible_person`             varchar(50)  DEFAULT NULL COMMENT '业务员',
    `cust_code`                      varchar(50)  DEFAULT NULL COMMENT '客户编码',
    `cust_name`                      varchar(50)  DEFAULT NULL COMMENT '客户名称',
    `product_code`                  varchar(50)  DEFAULT NULL COMMENT '产品编码',
    `product_name`                  varchar(50)  DEFAULT NULL COMMENT '产品名称',
    `unit_sales_price`               varchar(50)  DEFAULT NULL COMMENT '销售单价',
    `paper_code`                     varchar(50)  DEFAULT NULL COMMENT '纸张编码',
    `paper_name`                     varchar(255) DEFAULT NULL COMMENT '纸张名称',
    `gram_weight`                   varchar(50)  DEFAULT NULL COMMENT '克重',
    `paper_forecast_code`            varchar(255) DEFAULT NULL COMMENT '纸张预测编码',

    `id`                             int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `flag_deleted`                   int          DEFAULT '0' COMMENT '是否删除',
    `version`                        varchar(20)  DEFAULT NULL COMMENT '版本(纸张预测)',
    `create_by`                      varchar(50)  DEFAULT NULL COMMENT '创建人',
    `create_time`                    datetime     DEFAULT NULL COMMENT '创建时间',
    `update_by`                      varchar(50)  DEFAULT NULL COMMENT '修改人',
    `update_time`                    datetime     DEFAULT NULL COMMENT '修改时间',
    `domain_id`                      varchar(50)  DEFAULT NULL COMMENT '域id',
    PRIMARY KEY (`id`)
        USING BTREE
) COMMENT = '纸张预测_预测数据';


CREATE TABLE `crm_paper_forecast_version`
(
    `paper_forecast_code` varchar(255) DEFAULT NULL COMMENT '纸张预测编码',
    `status`              varchar(5)   DEFAULT NULL COMMENT '状态(0:待审批,1:审批中,2:生效,3:驳回)',
    `approver`            varchar(255) DEFAULT NULL COMMENT '审批人',
    `approver_time`       datetime     DEFAULT NULL COMMENT '审批时间',
    `forecast_version`    varchar(255) DEFAULT NULL COMMENT '预测版本',

    `update_time`         datetime     DEFAULT NULL COMMENT '修改时间',
    `update_by`           varchar(50)  DEFAULT NULL COMMENT '修改人',
    `create_time`         datetime     DEFAULT NULL COMMENT '创建时间',
    `create_by`           varchar(50)  DEFAULT NULL COMMENT '创建人',
    `version`             varchar(20)  DEFAULT NULL COMMENT '版本(纸张预测)',
    `flag_deleted`        int          DEFAULT '0' COMMENT '是否删除',
    `id`                  int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `domain_id`           varchar(50)  DEFAULT NULL COMMENT '域id',
    PRIMARY KEY (`id`)
) COMMENT = '纸张预测_版本';
