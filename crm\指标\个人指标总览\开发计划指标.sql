-- 开发数量
select count(distinct t1.cust_mnemonic_code) develop_count
from crm_cust_plan t1
         left join crm_cust_basic t2 on t1.cust_mnemonic_code = t2.cust_mnemonic_code and t1.flag_deleted = 0
where status in (3, 4, 5, 6)
  and t1.flag_deleted = 0
  and year(t1.update_time) = year(now())
  AND if(:admin,
         1,
         if(:cust_code_size > 0, t2.cust_code in (:cust_code_arr), 1)
      )
;
-- 开发成功数量
select count(distinct t1.cust_mnemonic_code) success_count
from crm_cust_plan t1
         left join crm_cust_basic t2 on t1.cust_mnemonic_code = t2.cust_mnemonic_code and t1.flag_deleted = 0
where t1.development_plan_result = '成功'
  and t1.flag_deleted = 0
  and year(t1.update_time) = year(now())
  AND if(:admin,
         1,
         if(:cust_code_size > 0, t2.cust_code in (:cust_code_arr), 1)
      )
;
-- 开发失败数量
select count(distinct t1.cust_mnemonic_code) fail_count
from crm_cust_plan t1
         left join crm_cust_basic t2 on t1.cust_mnemonic_code = t2.cust_mnemonic_code and t1.flag_deleted = 0
where t1.development_plan_result = '失败'
  and t1.flag_deleted = 0
  and year(t1.update_time) = year(now())
  AND if(:admin,
         1,
         if(:cust_code_size > 0, t2.cust_code in (:cust_code_arr), 1)
      )
;
-- 受限客户数量
select count(distinct cust_code) restrict_count
from crm_cust_basic
where cust_type = 4
  and flag_deleted = 0
  and year(update_time) = year(now())
  AND if(:admin,
         1,
         if(:cust_code_size > 0, cust_code in (:cust_code_arr), 1)
      )
;
-- 流失客户数量
select count(distinct cust_code) loss_count
from crm_lost_cust
where year(create_time) = year(now())
  AND if(:admin,
         1,
         if(:cust_code_size > 0, cust_code in (:cust_code_arr), 1)
      )
;
-- 客户投诉数量
select count(*) complaint_count
from crm_customer_complaint
where year(update_time) = year(now())
and flag_deleted=0
  AND if(:admin,
         1,
         if(:cust_code_size > 0, cust_code in (:cust_code_arr), 1)
      )
;



SELECT
    -- 开发数量
    (SELECT COUNT(DISTINCT t1.cust_mnemonic_code)
     FROM crm_cust_plan t1
              LEFT JOIN crm_cust_basic t2 ON t1.cust_mnemonic_code = t2.cust_mnemonic_code AND t1.flag_deleted = 0
     WHERE status IN (3, 4, 5, 6)
       AND t1.flag_deleted = 0
       AND YEAR(t1.update_time) = YEAR(NOW())
       AND IF(:admin, 1, IF(:cust_code_size > 0, t2.cust_code IN (:cust_code_arr), 1))
    ) AS develop_count,

    -- 开发成功数量
    (SELECT COUNT(DISTINCT t1.cust_mnemonic_code)
     FROM crm_cust_plan t1
              LEFT JOIN crm_cust_basic t2 ON t1.cust_mnemonic_code = t2.cust_mnemonic_code AND t1.flag_deleted = 0
     WHERE t1.development_plan_result = '成功'
       AND t1.flag_deleted = 0
       AND YEAR(t1.update_time) = YEAR(NOW())
       AND IF(:admin, 1, IF(:cust_code_size > 0, t2.cust_code IN (:cust_code_arr), 1))
    ) AS success_count,

    -- 开发失败数量
    (SELECT COUNT(DISTINCT t1.cust_mnemonic_code)
     FROM crm_cust_plan t1
              LEFT JOIN crm_cust_basic t2 ON t1.cust_mnemonic_code = t2.cust_mnemonic_code AND t1.flag_deleted = 0
     WHERE t1.development_plan_result = '失败'
       AND t1.flag_deleted = 0
       AND YEAR(t1.update_time) = YEAR(NOW())
       AND IF(:admin, 1, IF(:cust_code_size > 0, t2.cust_code IN (:cust_code_arr), 1))
    ) AS fail_count,

    -- 受限客户数量
    (SELECT COUNT(DISTINCT cust_code)
     FROM crm_cust_basic
     WHERE cust_type = 4
       AND flag_deleted = 0
       AND YEAR(update_time) = YEAR(NOW())
       AND IF(:admin, 1, IF(:cust_code_size > 0, cust_code IN (:cust_code_arr), 1))
    ) AS restrict_count,

    -- 流失客户数量
    (SELECT COUNT(DISTINCT cust_code)
     FROM crm_lost_cust
     WHERE YEAR(create_time) = YEAR(NOW())
       AND IF(:admin, 1, IF(:cust_code_size > 0, cust_code IN (:cust_code_arr), 1))
    ) AS loss_count,

    -- 客户投诉数量
    (SELECT COUNT(*)
     FROM crm_customer_complaint
     WHERE YEAR(update_time) = YEAR(NOW())
       AND flag_deleted = 0
       AND IF(:admin, 1, IF(:cust_code_size > 0, cust_code IN (:cust_code_arr), 1))
    ) AS complaint_count;

