-- 年度初步
select t1.years_months,
       t1.dept_code,
       t1.dept                   dept_name,
       t1.cust_manager_code      work_code,
       t1.cust_manager_name      responsible_person,
       t1.sales_assistant_code   sales_assistant_code,
       t2.product_code,
       t2.product_name,
       sum(
               t2.january_sale_money_forecast * t2.unit_sales_price +
               t2.february_sale_money_forecast * t2.unit_sales_price +
               t2.march_sale_money_forecast * t2.unit_sales_price +
               t2.april_sale_money_forecast * t2.unit_sales_price +
               t2.may_sale_money_forecast * t2.unit_sales_price +
               t2.june_sale_money_forecast * t2.unit_sales_price +
               t2.july_sale_money_forecast * t2.unit_sales_price +
               t2.august_sale_money_forecast * t2.unit_sales_price +
               t2.september_sale_money_forecast * t2.unit_sales_price +
               t2.october_sale_money_forecast * t2.unit_sales_price +
               t2.december_sale_money_forecast * t2.unit_sales_price +
               t2.november_sale_money_forecast * t2.unit_sales_price
       )                         sale_money_forecast,
       sum(
               t2.last_year_january_production * t2.unit_sales_price +
               t2.last_year_february_production * t2.unit_sales_price +
               t2.last_year_march_production * t2.unit_sales_price +
               t2.last_year_april_production * t2.unit_sales_price +
               t2.last_year_may_production * t2.unit_sales_price +
               t2.last_year_june_production * t2.unit_sales_price +
               t2.last_year_july_production * t2.unit_sales_price +
               t2.last_year_august_production * t2.unit_sales_price +
               t2.last_year_september_production * t2.unit_sales_price +
               t2.last_year_october_production * t2.unit_sales_price +
               t2.last_year_november_production * t2.unit_sales_price +
               t2.last_year_december_production * t2.unit_sales_price
       )                         last_year_sale_money,
       ifnull(t6.actual_sale, 0) actual_sale_money
from cockpit.ods_crm_paper_forecast t1
         join cockpit.ods_crm_paper_forecast_data t2
              on t1.paper_forecast_code = t2.paper_forecast_code
                  and t1.forecast_version = t2.forecast_version
         left join (select date_format(t3.create_time, '%Y')       update_year,
                           t5.username                             work_code,
                           t4.material_code                        product_code,
                           ifnull(sum(t4.amount_exclusive_tax), 0) actual_sale
                    from ods_crm_sales_order t3
                             left join ods_crm_sales_order_product t4 on t3.sales_order_code = t4.sales_order_code
                             left join cockpit.ods_sys_user t5 on t3.cust_manager_code = t5.user_id
                    where t3.flag_deleted = 0
                      and t4.flag_deleted = 0
                      and t5.del_flag = 0
                    group by update_year, work_code, product_code) t6
                   on t1.cust_manager_code = t6.work_code and t1.years_months = t6.update_year and
                      t6.product_code = t2.product_code
where t1.flag_deleted = 0
  and t2.flag_deleted = 0
  and t1.status = '2'
  and t1.forecast_dimension = '1'
group by years_months, t1.dept_code, t1.dept, t1.cust_manager_code, t1.cust_manager_name, t2.product_code,
         t2.product_name
;
-- 补全数据
select t1.`year`                                                       update_year,
       t2.dept_code,
       t2.dept_name,
       t2.work_code,
       t2.sale_person,
       t2.sales_assistant_code,
       t2.product_code,
       t2.product_name,
       coalesce(t3.sale_money_forecast, 0)                             sale_money_forecast,
       coalesce(t3.last_year_sale_money, 0)                            last_year_sale_money,
       coalesce(t3.actual_sale_money, 0)                               actual_sale_money,
       abs(coalesce(t3.actual_sale_money - t3.sale_money_forecast, 0)) sale_diff
from (select distinct `year` from dim_month) t1
         join
     (select distinct dept_code, dept_name, work_code, sale_person,sales_assistant_code, product_code, product_name
      from dwd_sale_forecast_year) t2
         left join dwd_sale_forecast_year t3
                   on t1.`year` = t3.update_year and t2.work_code = t3.work_code
order by update_year asc
;

-- 建表
drop table dwd_sale_forecast_year;
create table dwd_sale_forecast_year
(
    id                   int auto_increment comment '自增主键' primary key,
    update_year          varchar(10) comment '更新年',
    dept_code            varchar(100) comment '部门编码',
    dept_name            varchar(100) comment '部门名称',
    work_code            varchar(100) comment '工号',
    sale_person          varchar(100) comment '销售人',
    sales_assistant_code varchar(1000) comment '销售助理编码',
    product_code         varchar(100) comment '产品编码',
    product_name         varchar(100) comment '产品名称',
    sale_money_forecast  varchar(100) comment '销售预测额=同期导入的销售额',
    last_year_sale_money varchar(100) comment '去年同期=同期导入的生产数量*单价',
    actual_sale_money    varchar(100) comment '实际销售额',
    sale_diff            varchar(100) comment '销售额偏差'
) comment '销售预测_年度报表';

-- 销售人员维度
select update_year,
       dept_code,
       dept_name,
       work_code,
       sale_person,
       round(sum(sale_money_forecast), 2)                               sale_money_forecast,
       round(sum(last_year_sale_money), 2)                              last_year_sale_money,
       round(sum(actual_sale_money), 2)                                 actual_sale_money,
       concat(ifnull(round(100 * (sum(sale_money_forecast) - sum(last_year_sale_money)) / sum(last_year_sale_money), 2),
                     0), '%')                                           yoy,
       round(abs(sum(sale_money_forecast) - sum(actual_sale_money)), 2) sale_diff
from cockpit.dwd_sale_forecast_year
where if(:work_code_size, work_code in (:work_code_arr), 0)
  AND ((:year is null or :year = '') or (update_year = :year))
group by update_year, dept_code, dept_name, work_code, sale_person
order by update_year asc, work_code
limit :page_size offset :offset
;
-- 销售人员累计值
select '累计'                                                           update_year,
       ''                                                               dept_code,
       ''                                                               dept_name,
       ''                                                               work_code,
       ''                                                               sale_person,
       round(sum(sale_money_forecast), 2)                               sale_money_forecast,
       round(sum(last_year_sale_money), 2)                              last_year_sale_money,
       round(sum(actual_sale_money), 2)                                 actual_sale_money,
       concat(ifnull(round(100 * (sum(sale_money_forecast) - sum(last_year_sale_money)) / sum(last_year_sale_money), 2),
                     0), '%')                                           yoy,
       round(abs(sum(sale_money_forecast) - sum(actual_sale_money)), 2) sale_diff
from cockpit.dwd_sale_forecast_year
where if(:work_code_size, work_code in (:work_code_arr), 0)
  AND ((:year is null or :year = '') or (update_year = :year));


-- 部门维度
select update_year,
       dept_code,
       dept_name,
       round(sum(sale_money_forecast), 2)                               sale_money_forecast,
       round(sum(last_year_sale_money), 2)                              last_year_sale_money,
       round(sum(actual_sale_money), 2)                                 actual_sale_money,
       concat(ifnull(round(100 * (sum(sale_money_forecast) - sum(last_year_sale_money)) / sum(last_year_sale_money), 2),
                     0), '%')                                           yoy,
       round(abs(sum(sale_money_forecast) - sum(actual_sale_money)), 2) sale_diff
from cockpit.dwd_sale_forecast_year
where if(:work_code_size, work_code in (:work_code_arr), 0)
  AND ((:year is null or :year = '') or (update_year = :year))
group by update_year, dept_code, dept_name
order by update_year asc, dept_name
limit :page_size offset :offset
;
-- 部门累计值
select '累计'                                                           update_year,
       ''                                                               dept_code,
       ''                                                               dept_name,
       round(sum(sale_money_forecast), 2)                               sale_money_forecast,
       round(sum(last_year_sale_money), 2)                              last_year_sale_money,
       round(sum(actual_sale_money), 2)                                 actual_sale_money,
       concat(ifnull(round(100 * (sum(sale_money_forecast) - sum(last_year_sale_money)) / sum(last_year_sale_money), 2),
                     0), '%')                                           yoy,
       round(abs(sum(sale_money_forecast) - sum(actual_sale_money)), 2) sale_diff
from cockpit.dwd_sale_forecast_year
where if(:work_code_size, work_code in (:work_code_arr), 0)
  AND ((:year is null or :year = '') or (update_year = :year));

-- 产品维度
select update_year,
       product_code,
       product_name,
       round(sum(sale_money_forecast), 2)                               sale_money_forecast,
       round(sum(last_year_sale_money), 2)                              last_year_sale_money,
       round(sum(actual_sale_money), 2)                                 actual_sale_money,
       concat(ifnull(round(100 * (sum(sale_money_forecast) - sum(last_year_sale_money)) / sum(last_year_sale_money), 2),
                     0), '%')                                           yoy,
       round(abs(sum(sale_money_forecast) - sum(actual_sale_money)), 2) sale_diff
from cockpit.dwd_sale_forecast_year
where if(:work_code_size, work_code in (:work_code_arr), 0)
  AND ((:year is null or :year = '') or (update_year = :year))
  AND ((:product_code is null or :product_code = '') or (product_code = :product_code))
  AND ((:product_name is null or :product_name = '') or (product_name like concat('%', :product_name, '%')))
group by update_year, product_code, product_name
order by update_year asc, product_name
limit :page_size offset :offset;
-- 产品累计值
select '累计'                                                           update_year,
       ''                                                               product_code,
       ''                                                               product_name,
       round(sum(sale_money_forecast), 2)                               sale_money_forecast,
       round(sum(last_year_sale_money), 2)                              last_year_sale_money,
       round(sum(actual_sale_money), 2)                                 actual_sale_money,
       concat(ifnull(round(100 * (sum(sale_money_forecast) - sum(last_year_sale_money)) / sum(last_year_sale_money), 2),
                     0), '%')                                           yoy,
       round(abs(sum(sale_money_forecast) - sum(actual_sale_money)), 2) sale_diff
from cockpit.dwd_sale_forecast_year
where if(:work_code_size, work_code in (:work_code_arr), 0)
  AND ((:year is null or :year = '') or (update_year = :year))
  AND ((:product_code is null or :product_code = '') or (product_code = :product_code))
  AND ((:product_name is null or :product_name = '') or (product_name like concat('%', :product_name, '%')))
;
