-- 评审让步放行的批次
select a.production_batch_number production_batch_number, DATE_FORMAT(a.create_time, '%Y-%m') AS yuefen
from qms_abnormal_feedback a
         left join qms_abnormal_feedback_treatment b on a.id = b.abnormal_feedback_id
where a.abnormal_type = '生产过程异常'
  and b.QC_managers_measure = '让步接收'
  and year(a.create_time) = 2024
group by a.production_batch_number, DATE_FORMAT(a.create_time, '%Y-%m');

-- 进货检验的不合格评审放行失效批次
select a.production_batch_number production_batch_number, DATE_FORMAT(a.create_time, '%Y-%m') AS yuefen
from qms_abnormal_feedback a
         left join qms_abnormal_feedback_treatment b on a.id = b.abnormal_feedback_id
where a.abnormal_type = '生产过程异常'
  and b.QC_managers_measure = '报废'
  and abnormal_large_category ='原辅包异常'
  and year(a.create_time) = 2024
group by a.production_batch_number, DATE_FORMAT(a.create_time, '%Y-%m');

-- 整批让步放行
SELECT DISTINCT a.production_batch_number
FROM qms_abnormal_feedback a
         left join qms_abnormal_feedback_treatment b on a.id = b.abnormal_feedback_id
WHERE a.production_batch_number IS NOT NULL
  and b.QC_managers_measure = '让步接收'
  and b.is_zheng = '1'
GROUP BY a.production_batch_number;

-- 整批报废
SELECT DISTINCT a.production_batch_number
FROM qms_abnormal_feedback a
         left join qms_abnormal_feedback_treatment b on a.id = b.abnormal_feedback_id
WHERE a.production_batch_number IS NOT NULL
  and b.QC_managers_measure = '报废'
  and b.is_zheng = '1'
GROUP BY a.production_batch_number;

-- ETL
select DATE_FORMAT(a.create_time, '%Y-%m') AS                                                                yuefen,
       count(distinct
             if(a.abnormal_type = '生产过程异常' and b.QC_managers_measure = '让步接收', a.production_batch_number,
                null))                                                                                       can_1,
       sum(if(a.abnormal_type = '生产过程异常' and b.QC_managers_measure = '让步接收', a.abnormal_count, 0)) can_2,
       count(distinct if(a.abnormal_type = '生产过程异常' and a.abnormal_large_category = '原辅包异常' and
                         a.production_batch_number in
                         (select production_batch_number from mes_incoming_inspection where result = '1')
           , a.production_batch_number, null))                                                               can_3,
       0                                                                                                     can_4,
       count(distinct
             if(b.QC_managers_measure = '让步接收' and
                a.production_batch_number in (select batch_number from qms_customer_complaint), a.production_batch_number,
                null))                                                                                       can_5,
       count(distinct
             if(b.QC_managers_measure = '让步接收' and a.abnormal_subcategory = '色差' and
                a.production_batch_number in
                (select batch_number from qms_customer_complaint where defect_subclass = '色差'),
                a.production_batch_number,
                null))                                                                                       can_6,
       count(distinct
             if(b.QC_managers_measure = '报废'
                    and b.is_zheng = '1' and a.abnormal_subcategory = '色差' and
                a.production_batch_number in
                (select batch_number from qms_customer_complaint where defect_subclass = '色差'),
                a.production_batch_number,
                null))                                                                                       can_7,
       count(distinct
             if(b.QC_managers_measure = '偏差使用'
                    and b.is_zheng = '1',
                a.production_batch_number,
                null))                                                                                       can_8,
       count(distinct
             if(b.QC_managers_measure = '报废'
                    and b.is_zheng = '1',
                a.production_batch_number,
                null))                                                                                       can_9
from qms_abnormal_feedback a
         left join qms_abnormal_feedback_treatment b on a.id = b.abnormal_feedback_id
where a.flag_deleted = 0
  and b.flag_deleted = 0
  and ((:year IS NULL OR :year = '') OR (year(a.create_time) = :year))
group by DATE_FORMAT(a.create_time, '%Y-%m');

select abnormal_large_category
from qms_abnormal_feedback
group by abnormal_large_category;
select abnormal_subcategory
from qms_abnormal_feedback
group by abnormal_subcategory;

select abnormal_type
from qms_abnormal_feedback
group by abnormal_type;
