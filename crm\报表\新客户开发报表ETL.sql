-- 新客户开发
SELECT '西安环球'                    sale_company,
       t1.deparment_code,
       t1.deparment_name,
       t1.department_region          region,
       t1.cust_code,
       t1.cust_name,
       t1.cust_manager_code,
       t1.cust_manager_name,
       t1.cust_type,
       CASE
           WHEN t1.cust_type = 0 THEN '新建客户'
           WHEN t1.cust_type = 1 THEN '公海客户'
           WHEN t1.cust_type = 2 THEN '合作客户'
           WHEN t1.cust_type = 3 THEN '开发中客户'
           WHEN t1.cust_type = 4 THEN '受限客户'
           END                       cust_type_name,
       round(ifnull(t2.invoiced_amount, 0),2) invoiced_amount,
       round(ifnull(t3.received_money,0),2) received_money,
       round(ifnull(t4.received_money,0),2)  quater_received_money,
       round(ifnull(t5.received_money,0),2)  month_received_money,
       date_format(t1.create_time, '%Y')  metric_year,
       date_format(t1.create_time, '%Y-%m-%d')  development_date
FROM crm_cust_basic t1
         LEFT JOIN (select customer_no,
                           sum(ifnull(nnum, 0))     invoiced_quantity,
                           sum(ifnull(norigmny, 0)) invoiced_amount
                    from bip_outbound_order_detail bood
                             join (select csrcid, csrcbid, sum(nnum) nnum, sum(norigmny) norigmny
                                   from crm_sales_invoice_details
                                   where flag_deleted = 0
                                   group by csrcid, csrcbid) csid
                                  on bood.outbound_header = csid.csrcid and bood.outbound_line_id = csid.csrcbid
                    where bood.flag_deleted = 0
                    group by customer_no) t2 on t2.customer_no = t1.cust_code
         left join (select cust_code,
                           sum(money) received_money
                    from crm_sales_received_payments
                    where date_format(billdate,'%Y-%m-%d')>=DATE_FORMAT(CURRENT_DATE(), '%Y-01-01')
                    group by cust_code) t3 on t3.cust_code = t1.cust_code
         left join (select cust_code,
                           sum(money) received_money
                    from crm_sales_received_payments
                    where date_format(billdate,'%Y-%m-%d')>=(DATE_FORMAT(CURRENT_DATE(), '%Y-%m-01') - INTERVAL (MONTH(CURRENT_DATE())-1) % 3 MONTH)
                    group by cust_code) t4 on t4.cust_code = t1.cust_code
         left join (select cust_code,
                           sum(money) received_money
                    from crm_sales_received_payments
                    where date_format(billdate,'%Y-%m-%d')>=DATE_FORMAT(CURRENT_DATE(), '%Y-%m-01')
                    group by cust_code) t5 on t5.cust_code = t1.cust_code
WHERE t1.flag_deleted = 0
  and t1.cust_status = 2
  and t1.cust_type not in (0, 1)
  and date_format(t1.create_time, '%Y') = date_format(now(), '%Y')
  and round(ifnull(t2.invoiced_amount, 0),2)>0
order by round(ifnull(t3.received_money,0),2) desc
;

-- ETL建表
DROP TABLE IF EXISTS dws_new_customer;
truncate table dws_new_customer;
CREATE TABLE dws_new_customer
(
    id                    BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    sale_company          VARCHAR(50) COMMENT '销售公司',
    department_code       VARCHAR(50) COMMENT '部门编码',
    department_name       VARCHAR(100) COMMENT '部门名称',
    region                VARCHAR(100) COMMENT '区域',
    cust_manager_code     VARCHAR(50) COMMENT '销售人员编码',
    cust_manager_name     VARCHAR(100) COMMENT '销售人员姓名',
    cust_code             VARCHAR(50) COMMENT '客户编码',
    cust_name             VARCHAR(200) COMMENT '客户名称',
    cust_type             TINYINT COMMENT '客户类型(0-新建客户,1-公海客户,2-合作客户,3-开发中客户,4-受限客户)',
    cust_type_name        VARCHAR(50) COMMENT '客户类型名称',
    invoiced_amount       DECIMAL(18, 2) COMMENT '累计开票金额',
    received_money        DECIMAL(18, 2) COMMENT '累计回款金额',
    quater_received_money DECIMAL(18, 2) COMMENT '季度回款金额',
    month_received_money  DECIMAL(18, 2) COMMENT '月度回款金额',
    metric_year           VARCHAR(4) COMMENT '统计年份',
    development_date      varchar(10) COMMENT '开发日期',
    INDEX idx_cust_code (cust_code),
    index idx_cust_manager_code (cust_manager_code),
    INDEX idx_metric_year (metric_year)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='新客户开发';

select * from dws_new_customer dnc;
