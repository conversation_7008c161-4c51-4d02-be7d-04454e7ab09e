-- 预报价单 报价(预报价状态:0-草稿,1-已提交),(报价单状态:0-草稿,2-审批中,3-驳回,4-生效,5-作废)
select preliminary_quotation_code from crm_preliminary_quotation cpq group by preliminary_quotation_code
                                            having  count(preliminary_quotation_code)>1;
select * from crm_preliminary_quotation cpq where preliminary_quotation_code='BJ2502000510';
select material_code,product_version from crm_contract_management_product ccmp where contract_management_code='25020370';

-- 预报价表  crm_quotation_sheet crm_preliminary_quotation_product
-- 单条数据
-- 开发环境 多条数据:YB2501000016  ,单条数据:YB2501000008
select t2.* from crm_quotation_sheet t1
left join crm_preliminary_quotation_product t2 on t1.quotation_table_code=t2.quotation_table_code
where  t1.quotation_table_code='YB2501000095';

select material_code,product_version from crm_sales_order_product where sales_order_code='25020370A';
-- 多条数据 KS250106000001,KS250106000002
select material_code,product_version from crm_preliminary_quotation_product
         where preliminary_quotation_code in ('BJ2502000510') and flag_deleted=0;
select * from crm_quick_quote_product cqqp
where quick_quote_code='KS250106000001';

-- OA查询
select oa_id,quotation_code,cust_code from crm_preliminary_quotation where preliminary_quotation_code='BJ2502000510';
select * from crm_preliminary_quotation_product cpqp where preliminary_quotation_code='	25020370A';
select * from crm_preliminary_quotation_cust where preliminary_quotation_code='BJ2502000474';
select oa_id,quotation_code from crm_preliminary_quotation where oa_id='188087';
select * from crm_quotation cq where quotation_code='BJ2502000474';
select * from crm_contract_management ccm where quotation_code='BJ2502000510';

select
    cpqc.*,
    ifnull(ccb.cust_status, 0) as cust_status
from
    crm_preliminary_quotation_cust cpqc
        left join crm_cust_basic ccb on cpqc.cust_code = ccb.cust_code
        and ccb.flag_deleted = 0
where
    cpqc.flag_deleted = 0
  and ((:preliminary_quotation_code is null
    or :preliminary_quotation_code = '')
    or (cpqc.preliminary_quotation_code = :preliminary_quotation_code));
