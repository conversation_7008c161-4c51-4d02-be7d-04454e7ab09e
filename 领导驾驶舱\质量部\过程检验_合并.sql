-- 过程检验ETL
select update_month,
       process_code,
       process_name,
       max(first_inspect)           first_inspect,
       max(first_inspect_fail)      first_inspect_fail,
       max(first_inspect_deviation) first_inspect_deviation,
       max(first_inspect_pass_rate) first_inspect_pass_rate,
       max(avg_first_inspect)       avg_first_inspect,
       max(patrol_inspect)          patrol_inspect,
       max(patrol_fail)             patrol_fail,
       max(patrol_deviation)        patrol_deviation,
       max(patrol_pass_rate)        patrol_pass_rate,
       max(avg_patrol_inspect)      avg_patrol_inspect
from (select update_month,
             process_code,
             process_name,
             max(first_inspect)           first_inspect,
             max(first_inspect_fail)      first_inspect_fail,
             max(first_inspect_deviation) first_inspect_deviation,
             max(first_inspect_pass_rate) first_inspect_pass_rate,
             max(avg_first_inspect)       avg_first_inspect,
             0                            patrol_inspect,
             0                            patrol_fail,
             0                            patrol_deviation,
             0                            patrol_pass_rate,
             0                            avg_patrol_inspect
      from (select date_format(ifnull(qc_create_time, t1.create_time), '%Y-%m') update_month,
                   t1.production_processes                                      process_code,
                   t2.process_setting_name                                      process_name,
                   count(distinct production_batch_number)                      first_inspect,
                   0                                                            first_inspect_fail,
                   0                                                            first_inspect_deviation,
                   0                                                            first_inspect_pass_rate,
                   0                                                            avg_first_inspect
            from cockpit.ods_process_inspection t1
                     left join cockpit.ods_qms_process_template t2 on t1.production_processes = t2.process_setting_code
            where t1.flag_deleted = 0
              and t2.flag_deleted = 0
              and t1.inspection_type = 'jylx_3'
            group by update_month, process_code
            union
            select date_format(ifnull(qc_create_time, t1.create_time), '%Y-%m') update_month,
                   t1.production_processes                                      process_code,
                   t2.process_setting_name                                      process_name,
                   0                                                            first_inspect,
                   count(distinct production_batch_number)                      first_inspect_fail,
                   0                                                            first_inspect_deviation,
                   0                                                            first_inspect_pass_rate,
                   0                                                            avg_first_inspect
            from cockpit.ods_process_inspection t1
                     left join cockpit.ods_qms_process_template t2 on t1.production_processes = t2.process_setting_code
            where t1.flag_deleted = 0
              and t2.flag_deleted = 0
              and t1.inspection_type = 'jylx_3'
              and t1.result = 0
            group by update_month, process_code
            union
            select date_format(ifnull(qc_create_time, t1.create_time), '%Y-%m') update_month,
                   t1.production_processes                                      process_code,
                   t2.process_setting_name                                      process_name,
                   0                                                            first_inspect,
                   0                                                            first_inspect_fail,
                   count(distinct t1.production_batch_number)                   first_inspect_deviation,
                   0                                                            first_inspect_pass_rate,
                   0                                                            avg_first_inspect
            from cockpit.ods_process_inspection t1
                     left join cockpit.ods_qms_process_template t2 on t1.production_processes = t2.process_setting_code
                     left join cockpit.ods_abnormal_feedback t3 on t1.id = t3.mission_id
                     left join cockpit.ods_qms_abnormal_feedback_treatment t4 on t4.abnormal_feedback_id = t3.id
            where t1.flag_deleted = 0
              and t2.flag_deleted = 0
              and (t3.flag_deleted = 0 OR t3.flag_deleted IS NULL)
              and (t4.flag_deleted = 0 or t4.flag_deleted is null)
              and t1.inspection_type = 'jylx_3'
              and t1.result = 0
              and t4.QC_being_measure = '偏差使用'
            group by update_month, process_code
            union
            select date_format(ifnull(qc_create_time, t1.create_time), '%Y-%m') update_month,
                   t1.production_processes                                      process_code,
                   t2.process_setting_name                                      process_name,
                   0                                                            first_inspect,
                   0                                                            first_inspect_fail,
                   0                                                            first_inspect_deviation,
                   Round(100 * count(CASE when t1.result = 1 then job_id end) / count(job_id),
                         2)                                                     first_inspect_pass_rate,
                   0                                                            avg_first_inspect
            from cockpit.ods_process_inspection t1
                     left join cockpit.ods_qms_process_template t2 on t1.production_processes = t2.process_setting_code
            where t1.flag_deleted = 0
              and t2.flag_deleted = 0
              and t1.inspection_type = 'jylx_3'
            group by update_month, process_code
            union
            select date_format(ifnull(qc_create_time, t1.create_time), '%Y-%m') update_month,
                   t1.production_processes                                      process_code,
                   t2.process_setting_name                                      process_name,
                   0                                                            first_inspect,
                   0                                                            first_inspect_fail,
                   0                                                            first_inspect_deviation,
                   0                                                            first_inspect_pass_rate,
                   round(count(distinct t1.production_batch_number) /
                         (
                             count(distinct qc_create_by) + count(distinct sys_create_by)
                             )
                       , 2)                                                     avg_first_inspect
            from cockpit.ods_process_inspection t1
                     left join cockpit.ods_qms_process_template t2 on t1.production_processes = t2.process_setting_code
            where t1.flag_deleted = 0
              and t2.flag_deleted = 0
              and t1.inspection_type = 'jylx_3'
            group by update_month, process_code) temp1
      group by update_month, process_code
      union
      select update_month,
             process_code,
             process_name,
             0                       first_inspect,
             0                       first_inspect_fail,
             0                       first_inspect_deviation,
             0                       first_inspect_pass_rate,
             0                       avg_first_inspect,
             max(patrol_inspect)     patrol_inspect,
             max(patrol_fail)        patrol_fail,
             max(patrol_deviation)   patrol_deviation,
             max(patrol_pass_rate)   patrol_pass_rate,
             max(avg_patrol_inspect) avg_patrol_inspect
      from (select date_format(ifnull(qc_create_time, t1.create_time), '%Y-%m') update_month,
                   t1.production_processes                                      process_code,
                   t2.process_setting_name                                      process_name,
                   count(distinct production_batch_number)                      patrol_inspect,
                   0                                                            patrol_fail,
                   0                                                            patrol_deviation,
                   0                                                            patrol_pass_rate,
                   0                                                            avg_patrol_inspect
            from cockpit.ods_process_inspection t1
                     left join cockpit.ods_qms_process_template t2 on t1.production_processes = t2.process_setting_code
            where t1.flag_deleted = 0
              and t2.flag_deleted = 0
              and t1.inspection_type = 'jylx_4'
            group by update_month, process_code
            union
            select date_format(ifnull(qc_create_time, t1.create_time), '%Y-%m') update_month,
                   t1.production_processes                                      process_code,
                   t2.process_setting_name                                      process_name,
                   0                                                            patrol_inspect,
                   count(distinct production_batch_number)                      patrol_fail,
                   0                                                            patrol_deviation,
                   0                                                            patrol_pass_rate,
                   0                                                            avg_patrol_inspect
            from cockpit.ods_process_inspection t1
                     left join cockpit.ods_qms_process_template t2 on t1.production_processes = t2.process_setting_code
            where t1.flag_deleted = 0
              and t2.flag_deleted = 0
              and t1.inspection_type = 'jylx_4'
              and t1.result = 0
            group by update_month, process_code
            union
            select date_format(ifnull(qc_create_time, t1.create_time), '%Y-%m') update_month,
                   t1.production_processes                                      process_code,
                   t2.process_setting_name                                      process_name,
                   0                                                            patrol_inspect,
                   0                                                            patrol_fail,
                   count(distinct t1.production_batch_number)                   patrol_deviation,
                   0                                                            patrol_pass_rate,
                   0                                                            avg_patrol_inspect
            from cockpit.ods_process_inspection t1
                     left join cockpit.ods_qms_process_template t2 on t1.production_processes = t2.process_setting_code
                     left join cockpit.ods_abnormal_feedback t3 on t1.id = t3.mission_id
                     left join cockpit.ods_qms_abnormal_feedback_treatment t4 on t4.abnormal_feedback_id = t3.id
            where t1.flag_deleted = 0
              and t2.flag_deleted = 0
              and (t3.flag_deleted = 0 OR t3.flag_deleted IS NULL)
              and (t4.flag_deleted = 0 or t4.flag_deleted is null)
              and t1.inspection_type = 'jylx_4'
              and t1.result = 0
              and t4.QC_being_measure = '偏差使用'
            group by update_month, process_code
            union
            select date_format(ifnull(qc_create_time, t1.create_time), '%Y-%m')                   update_month,
                   t1.production_processes                                                        process_code,
                   t2.process_setting_name                                                        process_name,
                   0                                                                              patrol_inspect,
                   0                                                                              patrol_fail,
                   0                                                                              patrol_deviation,
                   Round(100 * count(CASE when t1.result = 1 then job_id end) / count(job_id), 2) patrol_pass_rate,
                   0                                                                              avg_patrol_inspect
            from cockpit.ods_process_inspection t1
                     left join cockpit.ods_qms_process_template t2 on t1.production_processes = t2.process_setting_code
            where t1.flag_deleted = 0
              and t2.flag_deleted = 0
              and t1.inspection_type = 'jylx_4'
            group by update_month, process_code
            union
            select date_format(ifnull(qc_create_time, t1.create_time), '%Y-%m') update_month,
                   t1.production_processes                                      process_code,
                   t2.process_setting_name                                      process_name,
                   0                                                            patrol_inspect,
                   0                                                            patrol_fail,
                   0                                                            patrol_deviation,
                   0                                                            patrol_pass_rate,
                   round(count(distinct t1.production_batch_number) /
                         (
                             count(distinct qc_create_by) + count(distinct sys_create_by)
                             )
                       , 2)                                                     avg_patrol_inspect
            from cockpit.ods_process_inspection t1
                     left join cockpit.ods_qms_process_template t2 on t1.production_processes = t2.process_setting_code
            where t1.flag_deleted = 0
              and t2.flag_deleted = 0
              and t1.inspection_type = 'jylx_4'
            group by update_month, process_code) temp2
      group by update_month, process_code) temp3
group by update_month, process_code, process_name
;

CREATE TABLE dwd_process_inspect_month
(
    id                      int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    update_month            varchar(255)   DEFAULT NULL COMMENT '更新月份yyyy-MM',
    process_code            varchar(255)   DEFAULT NULL COMMENT '工序代码',
    process_name            varchar(255)   DEFAULT NULL COMMENT '工序名称',
    first_inspect           int(11)        DEFAULT NULL COMMENT '月度首检次数',
    first_inspect_fail      int(11)        DEFAULT NULL COMMENT '首检不合格批次',
    first_inspect_deviation int(11)        DEFAULT NULL COMMENT '首检偏差放行批次',
    first_inspect_pass_rate decimal(5, 2)  DEFAULT NULL COMMENT '首检一次交验合格率',
    avg_first_inspect       decimal(10, 2) DEFAULT NULL COMMENT '人均首检批次',
    patrol_inspect          int(11)        DEFAULT NULL COMMENT '月度总巡检频次',
    patrol_fail             int(11)        DEFAULT NULL COMMENT '制程不合格发现批次',
    patrol_deviation        int(11)        DEFAULT NULL COMMENT '制程不合格偏差放行批次',
    patrol_pass_rate        decimal(5, 2)  DEFAULT NULL COMMENT '巡检合格率',
    avg_patrol_inspect      decimal(10, 2) DEFAULT NULL COMMENT '人均巡检频次',
    PRIMARY KEY (id)
) COMMENT = '过程检验统计月度';

-- 查询条件
select id,
       update_month,
       process_code,
       process_name,
       first_inspect,
       first_inspect_fail,
       first_inspect_deviation,
       first_inspect_pass_rate,
       avg_first_inspect,
       patrol_inspect,
       patrol_fail,
       patrol_deviation,
       patrol_pass_rate,
       avg_patrol_inspect
from cockpit.dwd_process_inspect_month
where ((:year is null or :year = '') or (left(update_month, 4) = :year))
  and ((:month is null or :month = '') or (right(update_month, 2) = :month))
  and ((:process_code is null or :process_code = '') or (process_code = :process_code))
order by update_month asc, process_code asc
limit :page_size offset :offset;


select *
from cockpit.dwd_process_inspect_month;
SELECT RIGHT('2024-08', 2) AS month;
SELECT left('2024-08', 4) AS year;
-- 以下为优化
SELECT DATE_FORMAT(IFNULL(qc_create_time, t1.create_time), '%Y-%m') AS update_month,
       t1.production_processes                                      AS process_code,
       t2.process_setting_name                                      AS process_name,
       t1.inspection_type,
       t1.result,
       t1.production_batch_number,
       t1.job_id,
       t1.qc_create_by,
       t1.sys_create_by,
       t3.flag_deleted                                              AS abn_flag_deleted,
       t4.flag_deleted                                              AS treat_flag_deleted,
       t4.QC_being_measure
FROM cockpit.ods_process_inspection t1
         LEFT JOIN
     cockpit.ods_qms_process_template t2 ON t1.production_processes = t2.process_setting_code
         LEFT JOIN
     cockpit.ods_abnormal_feedback t3 ON t1.id = t3.mission_id
         LEFT JOIN
     cockpit.ods_qms_abnormal_feedback_treatment t4 ON t4.abnormal_feedback_id = t3.id
WHERE t1.flag_deleted = 0
  AND t2.flag_deleted = 0
  AND (t3.flag_deleted = 0 OR t3.flag_deleted IS NULL)
  AND (t4.flag_deleted = 0 OR t4.flag_deleted IS NULL)
  and (t4.QC_being_measure != '' or t4.QC_being_measure is not null)
;


select date_format(ifnull(qc_create_time, t1.create_time), '%Y-%m') update_month,
       t1.production_processes                                      process_code,
       t2.process_setting_name                                      process_name,
       0                                                            patrol_inspect,
       0                                                            patrol_fail,
       count(distinct t1.production_batch_number)                   patrol_deviation,
       0                                                            patrol_pass_rate,
       0                                                            avg_patrol_inspect
from cockpit.ods_process_inspection t1
         left join cockpit.ods_qms_process_template t2 on t1.production_processes = t2.process_setting_code
         left join cockpit.ods_abnormal_feedback t3 on t1.id = t3.mission_id
         left join cockpit.ods_qms_abnormal_feedback_treatment t4 on t4.abnormal_feedback_id = t3.id
where t1.flag_deleted = 0
  and t2.flag_deleted = 0
  and (t3.flag_deleted = 0 OR t3.flag_deleted IS NULL)
  and (t4.flag_deleted = 0 or t4.flag_deleted is null)
  and t1.inspection_type = 'jylx_3'
  and t1.result = 0
  and t4.QC_being_measure = '偏差使用'
group by update_month, process_code;
