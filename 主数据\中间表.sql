-- 回调
select * from mdm_mom_afferent mma
where  module_name in ( '客户资信评估')
-- and data like '%188523%'
and data like '%189786%'
order by update_time desc;

select * from mdm_mom_afferent mma
where  module_name in ( 'VIP客户清单审批');
-- 报价
select * from mdm_mom_afferent mma
where  module_name in ( '报价合同评审')
  and  data like '%241902%'
order by update_time desc;

select * from mdm_mom_afferent mma
where  module_name in ( '订单关闭')
and data like '%203843%'
order by update_time desc;

select * from mdm_mom_afferent mma
where  module_name in ( '异常反馈单')
# and data like '%232926%'
order by update_time desc;

select * from mdm_mom_afferent mma
where  msg like '%Read timed out%'
order by update_time desc;

select * from mdm_mom_afferent mma
where  module_name  LIKE '%销售订单%'
 and data like '%25070274%'
order by update_time desc;

-- 外发 ----------------------------------------------------------------------------------------------
-- 日志查询
select *
from h3chq_crmbusiness1704287359505.crm_record
where logic = 'OA审批' and paramL like '%25020273A%';
select * from h3chq_crmbusiness1704287359505.crm_record cr where id='86101';
select * from h3chq_crmbusiness1704287359505.crm_sales_order cso where sales_order_code='25020273A';
select * from mdm_mom_outgoing mmo
where  data_Sources='物料新增多版本'
 and document_Number = 'ZDHY0003034'
 -- and state=2
order by update_time desc;

select * from mdm_mom_outgoing mmo
where  data_Sources='销售订单新增'
and document_Number like '25070274A%'
# and state=2
order by update_time desc;

select * from mdm_mom_outgoing mmo
where  state=4
-- and document_Number ='25030379A'
order by update_time desc;

select * from mdm_mom_outgoing mmo
where  data_Sources='AGV任务下发'
-- and document_Number ='25030379A'
order by update_time desc;

select data_Sources from mdm_mom_outgoing mmo
where  data_To='AGV'
-- and document_Number ='25030379A'
group by data_Sources;

select  id,operope_type,api_url,data_To,document_Number,data_Sources,data,state,link,cost_time,msg from   mdm_mom_outgoing where  state='0' and data_To!='AGV' limit 30;
select  id,operope_type,api_url,data_To,document_Number,data_Sources,data,state,link,cost_time,msg from   mdm_mom_outgoing where  state="0" and data_to!='AGV' limit 30;
select  id,operope_type,api_url,data_To,document_Number,data_Sources,data,state,link,cost_time,msg from   mdm_mom_outgoing where  state="0" ;

select * from mdm_mom_afferent mma where state=4;
select * from mdm_mom_afferent mma where data like '%MOM2025031401213%';
select client_code from mes_product_client ;
select *
from h3chq_pdmbusiness1704287270935.pdm_material pm
where material_code = 'ZDQX0001572';

select *
from h3chq_pdmbusiness1704287270935.pdm_product_version ppv
where material_code = 'ZDQX0001572'
  and product_version like '1.%'
order by product_version asc
limit 1
;

