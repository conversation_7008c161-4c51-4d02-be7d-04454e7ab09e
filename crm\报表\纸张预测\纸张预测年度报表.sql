-- 年度报表
select work_code,
       sales_assistant_code,
       production_factory,
       years_months,
       paper_name,
       paper_code,
       forecast_num,
       production_num,
       abs(forecast_num - production_num)                      forecast_diff,
       100 * abs(forecast_num - production_num) / forecast_num forecast_diff_rate,
       review_num,
       abs(review_num - production_num)                        review_diff,
       100 * abs(review_num - production_num) / review_num     review_diff_rate
from (select t2.work_code,
             t1.sales_assistant_code,
             t2.production_factory,
             t1.years_months,
             t2.paper_name,
             t2.paper_code,
             sum(
                     t2.year_january_forecast * rate +
                     t2.year_february_forecast * rate +
                     t2.year_march_forecast * rate +
                     t2.year_april_forecast * rate +
                     t2.year_may_forecast * rate +
                     t2.year_june_forecast * rate +
                     t2.year_july_forecast * rate +
                     t2.year_august_forecast * rate +
                     t2.year_september_forecast * rate +
                     t2.year_october_forecast * rate +
                     t2.year_november_forecast * rate +
                     t2.year_december_forecast * rate
             ) forecast_num,
             sum(
                     t2.last_year_january_production * rate +
                     t2.last_year_february_production * rate +
                     t2.last_year_march_production * rate +
                     t2.last_year_april_production * rate +
                     t2.last_year_may_production * rate +
                     t2.last_year_june_production * rate +
                     t2.last_year_july_production * rate +
                     t2.last_year_august_production * rate +
                     t2.last_year_september_production * rate +
                     t2.last_year_october_production * rate +
                     t2.last_year_november_production * rate +
                     t2.last_year_december_production * rate
             ) production_num,
             sum(
                     t2.year_january_review * rate +
                     t2.year_february_review * rate +
                     t2.year_march_review * rate +
                     t2.year_april_review * rate +
                     t2.year_may_review * rate +
                     t2.year_june_review * rate +
                     t2.year_july_review * rate +
                     t2.year_august_review * rate +
                     t2.year_september_review * rate +
                     t2.year_october_review * rate +
                     t2.year_november_review * rate +
                     t2.year_december_review * rate
             ) review_num
      from cockpit.ods_crm_paper_forecast t1
               join cockpit.ods_crm_paper_forecast_data t2
                    on t1.paper_forecast_code = t2.paper_forecast_code
                        and t1.forecast_version = t2.forecast_version
               LEFT JOIN (SELECT material_code,
                                 year(create_time)                                                           update_year,
                                 (1050000 / out_of_book) * gram_weight *
                                 CAST(SUBSTRING_INDEX(pushings, '*', 1) AS SIGNED) *
                                 CAST(SUBSTRING_INDEX(pushings, '*', -1) AS SIGNED) / 1000000000000 / 100 AS rate
                          FROM ods_pm_paper_list
                          WHERE flag_deleted = 0
                            and out_of_book is not null
                            and pushings is not null
                            and gram_weight is not null
                          GROUP BY material_code, YEAR(create_time)) t3
                         ON t2.paper_code = t3.material_code and t3.update_year = LEFT(t1.`years_months`, 4)
      where t1.flag_deleted = 0
        and t2.flag_deleted = 0
        and t1.status = '2'
        and t1.forecast_dimension = '1'
      group by t1.cust_manager_code,
               t2.production_factory,
               t1.`years_months`,
               t2.paper_name,
               t2.paper_code) temp
;

drop table dwd_paper_forecast_year;
create table dwd_paper_forecast_year
(
    id                   int auto_increment comment '自增主键' primary key,
    work_code            varchar(100)   null comment '工号',
    sales_assistant_code varchar(1000)  null comment '销售助理编码',
    production_factory   varchar(100)   null comment '工厂',
    update_year          varchar(100)   null comment '日期yyyy',
    paper_name           varchar(100)   null comment '纸张名称',
    paper_code           varchar(100)   null comment '纸张编码',
    forecast_num         varchar(100)   null comment '纸张预测数量',
    production_num       varchar(100)   null comment '生产数量',
    forecast_diff        varchar(100)   null comment '预测偏差',
    forecast_diff_rate   decimal(10, 2) null comment '预测偏差率',
    review_num           varchar(100)   null comment '回顾数量',
    review_diff          varchar(100)   null comment '回顾偏差',
    review_diff_rate     decimal(10, 2) null comment '回顾偏差率'
)
    comment '纸张预测年度报表';

-- 条件查询
select work_code,
       production_factory,
       update_year,
       paper_name,
       paper_code,
       round(forecast_num, 2)                    forecast_num,
       round(production_num, 2)                  production_num,
       round(forecast_diff, 2)                   forecast_diff,
       concat(round(forecast_diff_rate, 2), '%') forecast_diff_rate,
       round(review_num, 2)                      review_num,
       round(review_diff, 2)                     review_diff,
       concat(round(review_diff_rate, 2), '%')   review_diff_rate
from cockpit.dwd_paper_forecast_year
where ((:update_year IS NULL OR :update_year = '') OR (update_year = :update_year))
  and ((:paper_name is null OR :paper_name = '') or (paper_name like concat('%', :paper_name, '%')))
  and ((:paper_code is null OR :paper_code = '') or (paper_code = :paper_code))
  and if(:admin,
         if(:condition, work_code in (:work_code_arr), 1),
         if(:condition,
            work_code in (:work_code_arr) and sales_assistant_code like concat('%', :user_id, '%'),
            work_code in (:work_code_arr) or sales_assistant_code like concat('%', :user_id, '%'))
      )
limit :page_size offset :offset;


