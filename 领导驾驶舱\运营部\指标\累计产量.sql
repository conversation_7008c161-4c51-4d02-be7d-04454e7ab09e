-- 运营部-累计产量->dwd_yield_output_value  统计当月的累计产量
-- 旧指标
SELECT b.major_categories                                    as id,
       COALESCE(ROUND(SUM(COALESCE(c.report_num, 0)), 0), 0) AS value,
       DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s')               AS time
FROM pm_finsih_project_stock c
         LEFT JOIN pm_job_order a on c.pro_batch = a.production_batch_number
         LEFT JOIN pm_order_product b ON a.order_code = b.sales_number AND a.material_code = b.product_number

WHERE c.stock_type in (1, 4)
  and c.storage_status = 4
  AND YEAR(c.update_time) = YEAR(CURDATE())
  AND MONTH(c.update_time) = MONTH(CURDATE())
  and a.flag_deleted = 0
  and b.flag_deleted = 0
  and c.flag_deleted = 0
group by b.major_categories
;

SELECT
    COALESCE(SUM(COALESCE(report_num, 0)) , 0) AS value,
    DATE_FORMAT(NOW(),'%Y-%m-%d %H:%i:%s') AS time
FROM ods_pm_finsih_project_stock
WHERE stock_type = 1  and storage_status=4 AND check_flag = 1
  AND YEAR(create_time) = YEAR(CURDATE())
  AND MONTH(create_time) = MONTH(CURDATE());

-- 新指标 从明细表直接获取
select product_big_category,
       production_quantity,
       DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s') AS time
from dwd_production_value_month
where update_month = date_format(now(), '%Y-%m');

select * from dwd_yield_output_value;
