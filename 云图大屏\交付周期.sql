-- 订单下达时间
SELECT round((SUM(eee.`second`) / COUNT(eee.production_batch_number)) / 60 / 60 / 24, 1) as 'order_release_day',
       1                                                                                    id
FROM (SELECT ee.production_batch_number,
             ee.job_create_time,
             ee.create_time,
             unix_timestamp(ee.job_create_time) - unix_timestamp(ee.create_time) as 'second'
      FROM (SELECT c.production_batch_number, c.create_time, MIN(i.create_time) as 'job_create_time'
            FROM pm_job_order c
                     LEFT JOIN pm_job_order_management d
                               ON c.production_batch_number = d.production_batch_number
                     LEFT JOIN pm_job_detail i
                               ON d.task_code = i.task_code
            WHERE c.is_collage = 1
              AND c.flag_deleted = 0
              AND d.flag_deleted = 0
              AND i.flag_deleted = 0
              AND (d.status = 2 OR d.status = 3)
            GROUP BY c.production_batch_number, c.create_time
            UNION
            SELECT e.production_batch_number, e.create_time, MIN(j.create_time) as 'job_create_time'
            FROM pm_job_order e
                     LEFT JOIN pm_job_order_middle f
                               ON e.production_batch_number = f.production_batch_number
                     LEFT JOIN pm_job_order_management h
                               ON f.task_code = h.task_code
                     LEFT JOIN pm_job_detail j
                               ON h.task_code = j.task_code
            WHERE e.is_collage = 0
              AND e.flag_deleted = 0
              AND f.flag_deleted = 0
              AND h.flag_deleted = 0
              AND j.flag_deleted = 0
              AND (h.status = 2 OR h.status = 3)
            GROUP BY e.production_batch_number, e.create_time) ee) eee;

-- 半成品加工天数
WITH task_times AS (SELECT c.task_code,
                           MIN(d.create_time) AS min_create_time,
                           NULL               AS max_create_time
                    FROM pm_job_detail c
                             LEFT JOIN pm_oee_records d ON c.task_list_code = d.work_no AND d.action_properties = 3
                    WHERE (c.before_process_code IS NULL OR c.before_process_code = '')
                    GROUP BY c.task_code

                    UNION ALL

                    SELECT e.task_code,
                           NULL               AS min_create_time,
                           MAX(f.create_time) AS max_create_time
                    FROM pm_job_detail e
                             LEFT JOIN pm_final_batch_job_audit f ON e.id = f.job_strip_number
                    WHERE (e.after_process_code IS NULL OR e.after_process_code = '')
                    GROUP BY e.task_code),
     filtered_tasks AS (SELECT DISTINCT d.task_code
                        FROM pm_job_order c
                                 LEFT JOIN pm_job_order_management d
                                           ON c.production_batch_number = d.production_batch_number
                        WHERE c.is_collage = 1
                          AND c.flag_deleted = 0
                          AND d.flag_deleted = 0
                          AND c.production_order_status = 3
                          AND DATE(c.create_time) >= DATE_SUB(CURDATE(), INTERVAL 1 YEAR)
                          AND DATE(c.create_time) <= CURDATE()

                        UNION

                        SELECT DISTINCT f.task_code
                        FROM pm_job_order e
                                 LEFT JOIN pm_job_order_middle f
                                           ON e.production_batch_number = f.production_batch_number
                                 LEFT JOIN pm_job_order_management h ON f.task_code = h.task_code
                        WHERE e.is_collage = 0
                          AND f.flag_deleted = 0
                          AND h.flag_deleted = 0
                          AND e.production_order_status = 3
                          AND DATE(e.create_time) >= DATE_SUB(CURDATE(), INTERVAL 1 YEAR)
                          AND DATE(e.create_time) <= CURDATE()),
     valid_task_times AS (SELECT t1.task_code,
                                 t1.min_create_time,
                                 t1.max_create_time
                          FROM (SELECT task_code,
                                       min(min_create_time) min_create_time,
                                       max(max_create_time) max_create_time
                                FROM task_times
                                group by task_code
                                having min(min_create_time) is not null
                                   and max(max_create_time) is not null) t1
                                   JOIN filtered_tasks ft ON t1.task_code = ft.task_code)
SELECT round(AVG(TIMESTAMPDIFF(minute, min_create_time, max_create_time) / 24 / 60), 1) AS processing_day, 1 id
FROM valid_task_times;

-- 计划等待天数
select round(avg(abs(timestampdiff(minute, release_time, first_job_debug_time)) / 24 / 60), 1) planned_waiting_day,
       1                                                                                       id
from (SELECT c.task_code, d.release_time, MIN(e.create_time) as 'first_job_debug_time'
      FROM pm_job_order_management c
               LEFT JOIN (SELECT c1.task_code,
                                 MIN(c1.create_time) as 'release_time',
                                 c1.task_list_code   as 'first_task_list_code'
                          FROM pm_job_detail c1
                          WHERE c1.flag_deleted = 0
                            AND (c1.before_process_code is null or c1.before_process_code = '')
                          GROUP BY c1.task_code, c1.task_list_code) d
                         ON c.task_code = d.task_code
               LEFT JOIN pm_oee_records e
                         ON d.first_task_list_code = e.work_no
      WHERE c.`status` = 2
        AND e.action_properties = 6
      GROUP BY c.task_code, d.release_time) temp;

-- 成品到货
SELECT round(abs(avg(timestampdiff(minute, pd.create_time, ps.execution_time) / 24 / 60)),
             1) AS finished_product_delivery_days,
       1           id
FROM (SELECT ps.id,
             MAX(pfl.create_time) create_time
      FROM ods_pm_stowage ps
               LEFT JOIN ods_pm_flow_log pfl ON
          pfl.flow_id = ps.id
      WHERE ps.flag_deleted = 0
        AND pfl.flag_deleted = 0
        AND pfl.flow_name = '配载单管理'
        AND pfl.next_node_name = '已完成'
      GROUP BY pfl.flow_id) pd
         LEFT JOIN ods_pm_stowage ps ON
    ps.id = pd.id
         LEFT JOIN ods_pm_stowage_detail psd ON
    psd.parent_id = pd.id
         LEFT JOIN ods_pm_shipments_inform psi ON
    psi.shipments_code = psd.shipments_code
         LEFT JOIN ods_pm_shipments_inform_detail psid ON
    psi.id = psid.parent_id
WHERE psd.flag_deleted = 0
  AND psi.flag_deleted = 0
  AND psid.flag_deleted = 0
  AND pd.create_time >= DATE_SUB(curdate(), interval 1 year)
  and pd.create_time <= curdate()
;



