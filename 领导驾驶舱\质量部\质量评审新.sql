-- 进货检验的不合格评审放行失效批次
select d.production_batch_number              production_batch_number,
       DATE_FORMAT(a.create_time, '%Y-%m') AS yuefen
from ods_abnormal_feedback a
         left join ods_qms_abnormal_feedback_treatment b on a.id = b.abnormal_feedback_id
         left join ods_pm_material_module c on c.pro_batch_no = a.production_batch_number
         left join ods_mes_incoming_inspection d on d.product_code = c.finished_product_no
where a.abnormal_type = '生产过程异常'
  and b.QC_managers_measure = '报废'
  and a.abnormal_large_category = '原辅包异常'
  and d.result = '0'
  and year(a.create_time) = :year
group by a.production_batch_number, DATE_FORMAT(a.create_time, '%Y-%m');

-- 过程控制的不合格评审放行失效批次
-- 前工序
select t1.production_batch_number
from ods_abnormal_feedback t1
         left join ods_qms_abnormal_feedback_treatment t2 on t1.id = t2.abnormal_feedback_id
         join
     (select a.production_batch_number, b.QC_managers_measure, c.after_process_code
      from ods_abnormal_feedback a
               left join ods_qms_abnormal_feedback_treatment b on a.id = b.abnormal_feedback_id
               left join ods_pm_job_detail c
                         on c.process_code = a.process_code and c.production_batch_number = a.production_batch_number
      where b.QC_managers_measure = '让步接收') t3
     on t1.production_batch_number = t3.production_batch_number and t1.process_code = t3.after_process_code
         and t2.QC_managers_measure in ('报废', '返工')
;

select batch_number
from ods_qms_finished_inspection
where batch_number = 0
  and last_decide = 0;
;


-- ETL
select DATE_FORMAT(a.create_time, '%Y-%m') AS  update_month,
       count(distinct
             if(a.abnormal_type = '生产过程异常' and b.QC_managers_measure = '让步接收', a.production_batch_number,
                null))                         concession_release_batches,
       sum(if(a.abnormal_type = '生产过程异常' and b.QC_managers_measure = '让步接收', a.abnormal_count,
              0))                              concession_release_quantity,
       count(distinct if((b.QC_managers_measure = '让步接收' or b.QC_managers_measure = '让步接收') and
                         a.production_batch_number in
                         (select d.production_batch_number
                          from ods_abnormal_feedback a
                                   left join ods_qms_abnormal_feedback_treatment b on a.id = b.abnormal_feedback_id
                                   left join ods_pm_material_module c on c.pro_batch_no = a.production_batch_number
                                   left join ods_mes_incoming_inspection d on d.product_code = c.finished_product_no
                          where a.abnormal_type = '生产过程异常'
                            and b.QC_managers_measure = '报废'
                            and a.abnormal_large_category = '原辅包异常'
                            and d.result = '0')
           , a.production_batch_number, null)) incoming_inspection_failed_batches,
       count(distinct
             case
                 when (b.QC_managers_measure = '让步接收' and
                       a.production_batch_number in (select t1.production_batch_number
                                                     from ods_abnormal_feedback t1
                                                              left join ods_qms_abnormal_feedback_treatment t2
                                                                        on t1.id = t2.abnormal_feedback_id
                                                              join
                                                          (select a.production_batch_number,
                                                                  b.QC_managers_measure,
                                                                  c.after_process_code
                                                           from ods_abnormal_feedback a
                                                                    left join ods_qms_abnormal_feedback_treatment b on a.id = b.abnormal_feedback_id
                                                                    left join ods_pm_job_detail c
                                                                              on c.process_code = a.process_code and
                                                                                 c.production_batch_number =
                                                                                 a.production_batch_number
                                                           where b.QC_managers_measure = '让步接收') t3
                                                          on t1.production_batch_number = t3.production_batch_number and
                                                             t1.process_code = t3.after_process_code
                                                              and t2.QC_managers_measure in ('报废', '返工')))
                     then a.production_batch_number
                 when a.production_batch_number in
                      (select batch_number from ods_qms_finished_inspection where last_decide = 0)
                     then a.production_batch_number
                 end
       )                                       process_control_failed_batches,
       count(distinct
             if(b.QC_managers_measure = '让步接收' and
                a.production_batch_number in (select batch_number from ods_customer_complaint),
                a.production_batch_number,
                null))                         finished_goods_review,
       count(distinct
             if(b.QC_managers_measure = '让步接收' and a.abnormal_subcategory = '色差' and
                a.production_batch_number in
                (select batch_number from ods_customer_complaint where defect_subclass = '色差'),
                a.production_batch_number,
                null))                         color_difference_concession_batches,
       count(distinct
             if(b.QC_managers_measure = '报废'
                    and b.is_zheng = '1' and a.abnormal_subcategory = '色差' and
                a.production_batch_number in
                (select batch_number from ods_customer_complaint where defect_subclass = '色差'),
                a.production_batch_number,
                null))                         color_difference_scrap_batches,
       count(distinct
             if(b.QC_managers_measure = '偏差使用'
                    and b.is_zheng = '1',
                a.production_batch_number,
                null))                         whole_batch_concession,
       count(distinct
             if(b.QC_managers_measure = '报废'
                    and b.is_zheng = '1',
                a.production_batch_number,
                null))                         whole_batch_scrap
from ods_abnormal_feedback a
         left join ods_qms_abnormal_feedback_treatment b on a.id = b.abnormal_feedback_id
where a.flag_deleted = 0
  and b.flag_deleted = 0
group by DATE_FORMAT(a.create_time, '%Y-%m');

drop table dwd_quality_review;
CREATE TABLE dwd_quality_review
(
    id                                  INT AUTO_INCREMENT PRIMARY KEY COMMENT '唯一标识每一行记录',
    update_month                        varchar(10) NOT NULL COMMENT '记录数据所属的月份',
    concession_release_batches          INT DEFAULT 0 COMMENT '评审后让步放行的批次数量',
    concession_release_quantity         INT DEFAULT 0 COMMENT '评审后让步放行的产品总数量',
    incoming_inspection_failed_batches  INT DEFAULT 0 COMMENT '进货检验不合格但评审后放行失效的批次数量',
    process_control_failed_batches      INT DEFAULT 0 COMMENT '过程控制不合格但评审后放行失效的批次数量',
    finished_goods_review               INT DEFAULT 0 COMMENT '成品释放时进行的不合格评审次数或情况',
    color_difference_concession_batches INT DEFAULT 0 COMMENT '因色差原因而让步放行的批次数量',
    color_difference_scrap_batches      INT DEFAULT 0 COMMENT '因色差原因而整批报废的批次数量',
    whole_batch_concession              INT DEFAULT 0 COMMENT '整批让步放行的批次数量',
    whole_batch_scrap                   INT DEFAULT 0 COMMENT '整批报废的批次数量'
) comment '质量评审报表';


select t1.`year_month`                                  update_month,
       coalesce(concession_release_batches, 0)          concession_release_batches,
       coalesce(concession_release_quantity, 0)         concession_release_quantity,
       coalesce(incoming_inspection_failed_batches, 0)  incoming_inspection_failed_batches,
       coalesce(process_control_failed_batches, 0)      process_control_failed_batches,
       coalesce(finished_goods_review, 0)               finished_goods_review,
       coalesce(color_difference_concession_batches, 0) color_difference_concession_batches,
       coalesce(color_difference_scrap_batches, 0)      color_difference_scrap_batches,
       coalesce(whole_batch_concession, 0)              whole_batch_concession,
       coalesce(whole_batch_scrap, 0)                   whole_batch_scrap
from dim_month t1
         left join dwd_quality_review t2 on t1.`year_month` = t2.update_month;
select update_month,
       concession_release_batches,
       concession_release_quantity,
       incoming_inspection_failed_batches,
       process_control_failed_batches,
       finished_goods_review,
       color_difference_concession_batches,
       color_difference_scrap_batches,
       whole_batch_concession,
       whole_batch_scrap
from dwd_quality_review dqr
where left(update_month, 4) = :year;
select *
from dwd_quality_review dqr;



