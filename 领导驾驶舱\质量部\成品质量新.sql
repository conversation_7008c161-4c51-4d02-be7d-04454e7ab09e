-- 成品质量 ETL
select DATE_FORMAT(ifnull(finish_date, t1.update_time), '%Y-%m') as                        update_month,
       100 * SUM(if(large_category = '6876', received_quantity, 0)) /
       sum(if(large_category = '6876', put_material_num, 0))
                                                                 as                        carton_total_rate,
       100 * SUM(if(large_category = '6876' and is_collage = 1, received_quantity, 0)) /
       SUM(if(large_category = '6876' and is_collage = 1, put_material_num, 0))
                                                                 as                        carton_non_collage_rate,
       100 * SUM(if(large_category = '6876' and is_collage = 0, received_quantity, 0)) /
       SUM(if(large_category = '6876' and is_collage = 0, put_material_num, 0))
                                                                 as                        carton_collage_rate,
       100 * SUM(if(large_category = '6876' and received_quantity < 10000, received_quantity, 0)) /
       SUM(if(large_category = '6876' and received_quantity < 10000, put_material_num, 0))
                                                                 as                        carton_one_rate,
       100 * SUM(if(large_category = '6876' and received_quantity >= 10000 and received_quantity < 20000,
                    received_quantity, 0)) /
       SUM(if(large_category = '6876' and received_quantity >= 10000 and received_quantity < 20000,
              put_material_num, 0))
                                                                 as                        carton_two_rate,
       100 * SUM(if(large_category = '6876' and received_quantity >= 20000 and received_quantity < 50000,
                    received_quantity, 0)) /
       SUM(if(large_category = '6876' and received_quantity >= 20000 and received_quantity < 50000,
              put_material_num, 0))
                                                                 as                        carton_five_rate,
       100 * SUM(if(large_category = '6876' and received_quantity >= 50000 and received_quantity < 200000,
                    received_quantity, 0)) /
       SUM(if(large_category = '6876' and received_quantity >= 50000 and received_quantity < 200000,
              put_material_num, 0))
                                                                 as                        carton_twenty_rate,
       100 * SUM(if(large_category = '6876' and received_quantity >= 200000, received_quantity, 0)) /
       SUM(if(large_category = '6876' and received_quantity >= 200000, put_material_num, 0))
                                                                 as                        carton_twenty_up_rate,
       100 * SUM(if(large_category = '6901', received_quantity, 0)) /
       SUM(if(large_category = '6901', put_material_num, 0))
                                                                 as                        explain_total_rate,
       100 * SUM(if(large_category = '6901' and received_quantity < 10000, received_quantity, 0)) /
       SUM(if(large_category = '6901' and received_quantity < 10000, put_material_num, 0))
                                                                 as                        explain_one_rate,
       100 * SUM(if(large_category = '6901' and received_quantity >= 10000 and received_quantity < 50000,
                    received_quantity, 0)) /
       SUM(if(large_category = '6901' and received_quantity >= 10000 and received_quantity < 50000,
              put_material_num, 0))
                                                                 as                        explain_five_rate,
       100 * SUM(if(large_category = '6901' and received_quantity >= 50000 and received_quantity < 400000,
                    received_quantity, 0)) /
       SUM(if(large_category = '6901' and received_quantity >= 50000 and received_quantity < 400000,
              put_material_num, 0))
                                                                 as                        explain_forty_rate,
       100 * SUM(if(large_category = '6901' and received_quantity >= 400000, received_quantity, 0)) /
       SUM(if(large_category = '6901' and received_quantity >= 400000, put_material_num, 0))
                                                                 as                        explain_forty_up_rate,
       100 - 100 * count(distinct if(large_category = '6876' and t1.production_batch_number in
                                                                 (select batch_number
                                                                  from cockpit.ods_qms_finished_inspection
                                                                  where flag_deleted = 0
                                                                    and last_decide = '0'),
                                     t1.production_batch_number, null)) /
             count(distinct if(large_category = '6876', t1.production_batch_number, null)) carton_pass_rate,
       100 - 100 * count(distinct if(large_category = '6901' and t1.production_batch_number in
                                                                 (select batch_number
                                                                  from cockpit.ods_qms_finished_inspection
                                                                  where flag_deleted = 0
                                                                    and last_decide = '0'),
                                     t1.production_batch_number, null)) /
             count(distinct if(large_category = '6901', t1.production_batch_number, null)) explain_pass_rate
from cockpit.ods_pm_job_order t1
         left join ods_pm_feed_quantity t2 on
    t1.production_batch_number = t2.production_batch_number
where t1.flag_deleted = 0
  and production_order_status = '3'
  and source_type = '1'
  and cast(t1.received_quantity as unsigned) <= cast(t2.put_material_num as unsigned)
group by update_month
;
select `year_month` update_month,
       COALESCE(temp.carton_total_rate,0) carton_total_rate,
       COALESCE(temp.carton_non_collage_rate,0) carton_non_collage_rate,
       COALESCE(temp.carton_collage_rate,0) carton_collage_rate,
       COALESCE(temp.carton_one_rate,0) carton_one_rate,
       COALESCE(temp.carton_two_rate,0) carton_two_rate,
       COALESCE(temp.carton_five_rate,0) carton_five_rate,
       COALESCE(temp.carton_twenty_rate,0) carton_twenty_rate,
       COALESCE(temp.carton_twenty_up_rate,0) carton_twenty_up_rate,
       COALESCE(temp.explain_total_rate,0) explain_total_rate,
       COALESCE(temp.explain_one_rate,0) explain_one_rate,
       COALESCE(temp.explain_five_rate,0) explain_five_rate,
       COALESCE(temp.explain_forty_rate,0) explain_forty_rate,
       COALESCE(temp.explain_forty_up_rate,0) explain_forty_up_rate,
       COALESCE(temp.carton_pass_rate,0) carton_pass_rate,
       COALESCE(temp.explain_pass_rate,0) explain_pass_rate
from  dim_month
left join (
    select DATE_FORMAT(ifnull(finish_date, t1.update_time), '%Y-%m') as                        update_month,
           100 * SUM(if(large_category = '6876', received_quantity, 0)) /
           sum(if(large_category = '6876', put_material_num, 0))
                                                                     as                        carton_total_rate,
           100 * SUM(if(large_category = '6876' and is_collage = 1, received_quantity, 0)) /
           SUM(if(large_category = '6876' and is_collage = 1, put_material_num, 0))
                                                                     as                        carton_non_collage_rate,
           100 * SUM(if(large_category = '6876' and is_collage = 0, received_quantity, 0)) /
           SUM(if(large_category = '6876' and is_collage = 0, put_material_num, 0))
                                                                     as                        carton_collage_rate,
           100 * SUM(if(large_category = '6876' and received_quantity < 10000, received_quantity, 0)) /
           SUM(if(large_category = '6876' and received_quantity < 10000, put_material_num, 0))
                                                                     as                        carton_one_rate,
           100 * SUM(if(large_category = '6876' and received_quantity >= 10000 and received_quantity < 20000,
                        received_quantity, 0)) /
           SUM(if(large_category = '6876' and received_quantity >= 10000 and received_quantity < 20000,
                  put_material_num, 0))
                                                                     as                        carton_two_rate,
           100 * SUM(if(large_category = '6876' and received_quantity >= 20000 and received_quantity < 50000,
                        received_quantity, 0)) /
           SUM(if(large_category = '6876' and received_quantity >= 20000 and received_quantity < 50000,
                  put_material_num, 0))
                                                                     as                        carton_five_rate,
           100 * SUM(if(large_category = '6876' and received_quantity >= 50000 and received_quantity < 200000,
                        received_quantity, 0)) /
           SUM(if(large_category = '6876' and received_quantity >= 50000 and received_quantity < 200000,
                  put_material_num, 0))
                                                                     as                        carton_twenty_rate,
           100 * SUM(if(large_category = '6876' and received_quantity >= 200000, received_quantity, 0)) /
           SUM(if(large_category = '6876' and received_quantity >= 200000, put_material_num, 0))
                                                                     as                        carton_twenty_up_rate,
           100 * SUM(if(large_category = '6901', received_quantity, 0)) /
           SUM(if(large_category = '6901', put_material_num, 0))
                                                                     as                        explain_total_rate,
           100 * SUM(if(large_category = '6901' and received_quantity < 10000, received_quantity, 0)) /
           SUM(if(large_category = '6901' and received_quantity < 10000, put_material_num, 0))
                                                                     as                        explain_one_rate,
           100 * SUM(if(large_category = '6901' and received_quantity >= 10000 and received_quantity < 50000,
                        received_quantity, 0)) /
           SUM(if(large_category = '6901' and received_quantity >= 10000 and received_quantity < 50000,
                  put_material_num, 0))
                                                                     as                        explain_five_rate,
           100 * SUM(if(large_category = '6901' and received_quantity >= 50000 and received_quantity < 400000,
                        received_quantity, 0)) /
           SUM(if(large_category = '6901' and received_quantity >= 50000 and received_quantity < 400000,
                  put_material_num, 0))
                                                                     as                        explain_forty_rate,
           100 * SUM(if(large_category = '6901' and received_quantity >= 400000, received_quantity, 0)) /
           SUM(if(large_category = '6901' and received_quantity >= 400000, put_material_num, 0))
                                                                     as                        explain_forty_up_rate,
           100 - 100 * count(distinct if(large_category = '6876' and t1.production_batch_number in
                                                                     (select batch_number
                                                                      from cockpit.ods_qms_finished_inspection
                                                                      where flag_deleted = 0
                                                                        and last_decide = '0'),
                                         t1.production_batch_number, null)) /
                 count(distinct if(large_category = '6876', t1.production_batch_number, null)) carton_pass_rate,
           100 - 100 * count(distinct if(large_category = '6901' and t1.production_batch_number in
                                                                     (select batch_number
                                                                      from cockpit.ods_qms_finished_inspection
                                                                      where flag_deleted = 0
                                                                        and last_decide = '0'),
                                         t1.production_batch_number, null)) /
                 count(distinct if(large_category = '6901', t1.production_batch_number, null)) explain_pass_rate
    from cockpit.ods_pm_job_order t1
             left join ods_pm_feed_quantity t2 on
        t1.production_batch_number = t2.production_batch_number
    where t1.flag_deleted = 0
      and production_order_status = '3'
      and source_type = '1'
      and cast(t1.received_quantity as unsigned) <= cast(t2.put_material_num as unsigned)
    group by update_month
)temp on `year_month`=temp.update_month
order by `year_month`;


select t1.production_batch_number, t1.create_time, t1.received_quantity, t2.put_material_num
from cockpit.ods_pm_job_order t1
         left join ods_pm_feed_quantity t2 on
    t1.production_batch_number = t2.production_batch_number
where t1.flag_deleted = 0
  and production_order_status = '3'
  and source_type = '1'
  and cast(t1.received_quantity as unsigned) > cast(t2.put_material_num as unsigned);


select t1.production_batch_number, t1.create_time
from cockpit.ods_pm_job_order t1
         left join ods_pm_feed_quantity t2 on
    t1.production_batch_number = t2.production_batch_number
where t1.flag_deleted = 0
  and production_order_status = '3'
  and source_type = '1'
  and t2.put_material_num = 0;


-- 成品质量表
drop table dwd_product_quality_month;
create table dwd_product_quality_month
(
    `id`                    int         NOT NULL AUTO_INCREMENT primary key COMMENT '自增主键',
    update_month            varchar(10) NOT NULL comment '更新月份yyyy-MM',
    carton_total_rate       decimal(10, 2) DEFAULT null comment '综合合格品收率（折叠纸盒）',
    carton_non_collage_rate decimal(10, 2) DEFAULT null comment '非搭印合格品收率（折叠纸盒）',
    carton_collage_rate     decimal(10, 2) DEFAULT null comment '搭印合格品收率（折叠纸盒）',
    carton_one_rate         decimal(10, 2) DEFAULT null comment '合格品收率（折叠纸盒1万只以下）',
    carton_two_rate         decimal(10, 2) DEFAULT null comment '合格品收率（折叠纸盒1万只~2万只）',
    carton_five_rate        decimal(10, 2) DEFAULT null comment '合格品收率（折叠纸盒2万只~5万只）',
    carton_twenty_rate      decimal(10, 2) DEFAULT null comment '合格品收率（折叠纸盒5万只~20万只）',
    carton_twenty_up_rate   decimal(10, 2) DEFAULT null comment '合格品收率（折叠纸盒20万只以上）',
    explain_total_rate      decimal(10, 2) DEFAULT null comment '合格品收率（说明书类）',
    explain_one_rate        decimal(10, 2) DEFAULT null comment '合格品收率（说明书类1万张以下）',
    explain_five_rate       decimal(10, 2) DEFAULT null comment '合格品收率（说明书类 1万~5万）',
    explain_forty_rate      decimal(10, 2) DEFAULT null comment '合格品收率（说明书类 5万~40万）',
    explain_forty_up_rate   decimal(10, 2) DEFAULT null comment '合格品收率（说明书类 40万张以上）',
    carton_pass_rate        decimal(10, 2) DEFAULT null comment '综合合格品收率（折叠纸盒）',
    explain_pass_rate       decimal(10, 2) DEFAULT null comment '综合合格品收率（说明书）'
)
    comment '成品质量表';

select update_month,
       concat(carton_total_rate, '%')       carton_total_rate,
       concat(carton_non_collage_rate, '%') carton_non_collage_rate,
       concat(carton_collage_rate, '%')     carton_collage_rate,
       concat(carton_one_rate, '%')         carton_one_rate,
       concat(carton_two_rate, '%')         carton_two_rate,
       concat(carton_five_rate, '%')        carton_five_rate,
       concat(carton_twenty_rate, '%')      carton_twenty_rate,
       concat(carton_twenty_up_rate, '%')   carton_twenty_up_rate,
       concat(explain_total_rate, '%')      explain_total_rate,
       concat(explain_one_rate, '%')        explain_one_rate,
       concat(explain_five_rate, '%')       explain_five_rate,
       concat(explain_forty_rate, '%')      explain_forty_rate,
       concat(explain_forty_up_rate, '%')   explain_forty_up_rate,
       concat(carton_pass_rate, '%')        carton_pass_rate,
       concat(explain_pass_rate, '%')       explain_pass_rate
from cockpit.dwd_product_quality_month
where left(update_month, 4) = :year
order by update_month asc;

-- 投料关联表
drop table ods_pm_feed_quantity;
create table ods_pm_feed_quantity
(
    put_material_num        varchar(255)  null comment '投料量',
    production_batch_number varchar(255)  null comment '生产批次',
    id                      int auto_increment comment '自增主键' primary key,
    flag_deleted            int default 0 null comment '是否删除',
    version                 varchar(20)   null comment '版本',
    create_by               varchar(50)   null comment '创建人',
    create_time             datetime      null comment '创建时间',
    update_by               varchar(50)   null comment '修改人',
    update_time             datetime      null comment '修改时间',
    domain_id               varchar(50)   null comment '域id'
) comment '投料量';
