-- 把开票明细的对应数据删除,把对应开票申请的bip开票数量回滚.
SELECT
    csop.contract_product_line_number,
    csid.*,
    CASE
        WHEN csid.fstatusflag = 1 THEN '自由'
        WHEN csid.fstatusflag = 2 THEN '审批通过'
        WHEN csid.fstatusflag = 3 THEN '冻结'
        WHEN csid.fstatusflag = 4 THEN '关闭'
        WHEN csid.fstatusflag = 7 THEN '审批中'
        WHEN csid.fstatusflag = 8 THEN '审批不通过'
        WHEN csid.fstatusflag = 5 THEN '失效'
        ELSE csid.fstatusflag
        END status_name
FROM
    crm_sales_order cso
        LEFT JOIN crm_sales_invoice_details csid ON
        csid.csaleorderid = cso.id
        LEFT JOIN crm_sales_order_product csop ON
        csop.id = csid.csaleorderbid
WHERE
    cso.flag_deleted = 0
  AND csid.flag_deleted = 0
  AND cso.sales_order_code = '25020169A';
-- 更新crm_sales_invoice_details表delete_flag=1,获取nnum
    select * from crm_sales_invoice_details where flag_deleted=0 and csrcbid='1001A510000000FDIT8S';

select t1.id crm_sales_invoice_details_id ,
       t2.id invoice_application_detail_id,
       if(t2.actual_invoice_quantity-t1.nnum<0,0,t2.actual_invoice_quantity-t1.nnum) actual_invoice_quantity from crm_sales_invoice_details t1
         left join invoice_application_detail t2 on t1.dsfbtzj_b=t2.id and t2.flag_deleted=0
         where t1.flag_deleted=0
and t2.apply_no='VR202506013477';
;
select status from crm_sales_order cso where sales_order_code='25030122A';


-- 把对应开票申请的bip开票数量回滚
select t1.*, t2.* from invoice_application t1
    left join invoice_application_detail t2 on t1.apply_no=t2.apply_no and t2.flag_deleted=0
where t1.flag_deleted=0
and t2.outbound_line_id='1001A510000000FDIT8S'
;


select * from invoice_application where apply_no='VR202502200002';
select * from invoice_application_detail where outbound_line_id='1001A510000000FDIT8S';
--
select outbound_line_id,count(*) from invoice_application_detail where flag_deleted=0 group by outbound_line_id having  count(outbound_line_id)>1;

