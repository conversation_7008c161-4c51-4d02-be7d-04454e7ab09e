-- ETL:运营部-周发货总结明细-实际发货明细-月->dwd_shipment_detail_month
SELECT
    obd.production_batch_number,
    obd.customer_name,
    obd.product_code,
    obd.product_name,
    obd.order_quantity,
    obd.planned_shipment_date,
    sbd.actual_shipment_date,
    sbd.actual_shipment_quantity,
    obd.order_status,
    2 detail_type,
    sbd.update_year,
    sbd.month_of_year
FROM
    (
        SELECT
            YEAR(pfl.create_time) update_year,
            MONTH(pfl.create_time) month_of_year,
            bd.batch_no AS production_batch_number,
            pfl.create_time actual_shipment_date,
            SUM(psd.num_reality) actual_shipment_quantity
        FROM
            (
                SELECT
                    psd.batch_no,
                    MAX(msd.max_status_id) AS max_status_id,
                    MAX(psd.id) AS max_psd_id
                FROM
                    (
                        SELECT
                            pfl.flow_id,
                            MAX(pfl.id) AS max_status_id
                        FROM
                            ods_pm_flow_log pfl
                        WHERE
                            pfl.flag_deleted = 0
                          AND pfl.flow_name = '配载单管理'
                          AND pfl.next_node_name = '已出库'
                          AND YEAR(pfl.create_time) = YEAR(CURDATE())
                        GROUP BY
                            pfl.flow_id
                    ) msd
                        LEFT JOIN ods_pm_stowage_detail psd ON
                        psd.parent_id = msd.flow_id
                WHERE
                    psd.flag_deleted = 0
                GROUP BY
                    psd.batch_no
            ) bd
                LEFT JOIN ods_pm_flow_log pfl ON
                pfl.id = bd.max_status_id
                LEFT JOIN ods_pm_stowage_detail psd ON
                psd.id = bd.max_psd_id
        GROUP BY
            YEAR(pfl.create_time),
            MONTH(pfl.create_time),
            bd.batch_no
    ) sbd
        INNER JOIN (
        SELECT
            opjo.production_batch_number,
            opjo.customer_name,
            opjo.material_code product_code,
            opjo.material_name product_name,
            opjo.release_quantity order_quantity,
            opjo.planned_delivery_date planned_shipment_date,
            CASE
                WHEN opjo.production_order_status = '0' THEN '未生成作业单'
                WHEN opjo.production_order_status = '1' THEN '有作业单未下达'
                WHEN opjo.production_order_status = '2' THEN '作业单全部下达'
                WHEN opjo.production_order_status = '3' THEN '已完成'
                WHEN opjo.production_order_status = '4' THEN '转外协'
                ELSE opjo.production_order_status
                END order_status,
            YEAR(opjo.planned_delivery_date) update_year,
            MONTH(opjo.planned_delivery_date) month_of_year
        FROM
            ods_pm_job_order opjo
        WHERE
            opjo.flag_deleted = 0
          AND opjo.source_type = '1'
          AND YEAR(opjo.planned_delivery_date) = YEAR(CURDATE())
    ) obd ON
        obd.update_year = sbd.update_year
            AND obd.month_of_year = sbd.month_of_year
            AND obd.production_batch_number = sbd.production_batch_number;
-- ETL:运营部-周发货总结-计划发货批次-月->dwd_shipment_summary_month
SELECT
    YEAR(opjo.planned_delivery_date) update_year,
    MONTH(opjo.planned_delivery_date) month_of_year,
    COUNT(*) AS planned_shipment_batches
FROM
    ods_pm_job_order opjo
WHERE
    opjo.flag_deleted = 0
  AND YEAR(opjo.planned_delivery_date) = YEAR(CURDATE())
GROUP BY
    YEAR(opjo.planned_delivery_date),
    MONTH(opjo.planned_delivery_date);


-- ETL:运营部发货达成率月度报表->dwd_shipment_achievement_rate_month
-- 发货达成率计算
SELECT
    CONCAT(dssm.update_year, '-', IF(dssm.month_of_year < 10, concat('0', dssm.month_of_year), dssm.month_of_year)) update_month,
    CASE
        WHEN COALESCE(rd.count_shipment_batches, 0) = 0 THEN 0
        ELSE ROUND(100 * rd.count_shipment_batches / dssm.planned_shipment_batches, 2)
        END achievement_rate,
    0 achievement_rate_yoy,
    0 achievement_rate_mom
FROM
    cockpit.dwd_shipment_summary_month dssm
        LEFT JOIN
    (
        SELECT
            update_year,
            month_of_year,
            COUNT(*) count_shipment_batches
        FROM
            cockpit.dwd_shipment_detail_month dsdm2
        WHERE
            dsdm2.update_year = YEAR(CURDATE())
    AND dsdm2.detail_type = '2'
		AND dsdm2.actual_shipment_date < DATE_ADD(planned_shipment_date, INTERVAL IFNULL((SELECT dict_value FROM ods_sys_dict_biz osdb WHERE osdb.flag_deleted = 0 AND osdb.dict_code = 'summary_set_day'
LIMIT 1), 0)  DAY)
GROUP BY
    dsdm2.update_year,
    dsdm2.month_of_year
    ) rd ON
    rd.update_year = dssm.update_year
    AND rd.month_of_year = dssm.month_of_year
WHERE
    dssm.update_year = YEAR(CURDATE())
ORDER BY
    dssm.month_of_year;



