select cust_code from crm_sales_order cso where sales_order_code='25020282A';
select cust_version,cust_type,cust_status,cust_code,ccb.* from crm_cust_basic ccb where cust_code='C000262';


WITH temp_date AS (
    SELECT
        DISTINCT
        ia.split_order_no,
        cso.sales_order_code
    FROM
        invoice_application ia,
        crm_sales_order cso
    WHERE
        cso.flag_deleted = 0
      AND ia.flag_deleted = 0
      AND ia.status IN ('0', '3', '4')
      AND INSTR(CONCAT(',', ia.split_order_no, ','), CONCAT(',', cso.sales_order_code, ',')) > 0
)
SELECT
    DISTINCT
    cso.id,
    cso.sales_order_code,
    CASE
        WHEN cso.status IN ('10', '8', '7', '6') THEN cso.status
        WHEN td.split_order_no IS NOT NULL THEN '11'
        ELSE cso.status
        END status,
    CASE
        WHEN cso.status = '10' THEN '已删除'
        WHEN cso.status = '8' THEN '已取消'
        WHEN cso.status = '7' THEN '已关闭'
        WHEN cso.status = '6' THEN '已开票'
        WHEN td.split_order_no IS NOT NULL THEN '已发货未开票'
        WHEN cso.status = '0' THEN '已拆分'
        WHEN cso.status = '1' THEN '已下达'
        WHEN cso.status = '2' THEN '已排产'
        WHEN cso.status = '3' THEN '已领料'
        WHEN cso.status = '4' THEN '生产中'
        WHEN cso.status = '5' THEN '已入库'
        ELSE cso.status
        END status_name,
    cso.stock_order_code,
    cso.cust_code,
    cso.cust_name,
    ccb.cust_vip,
    cso.printing_price,
    ccb.sales_assistant_name,
    ccb.cust_manager_name,
    cso.create_time,
    cso.show_create_by,
    cso.cancellation_reason,
    cso.remark,
    cso.quotation_code,
    cso.cust_manager_code,
    cso.sales_assistant_code,
    cpq.imposition_quotation,
    cso.oa_out
FROM
    crm_sales_order_product csop
        LEFT JOIN crm_sales_order cso ON
        cso.sales_order_code = csop.sales_order_code
        LEFT JOIN temp_date td ON
        td.sales_order_code = cso.sales_order_code
        LEFT JOIN crm_cust_basic ccb ON
        ccb.cust_code = cso.cust_code
            AND ccb.flag_deleted = 0
            AND ccb.cust_type not in  ('0','1')
        LEFT JOIN crm_preliminary_quotation cpq ON
        cpq.preliminary_quotation_code = cso.quotation_code
where cso.sales_order_code='25020282A';
