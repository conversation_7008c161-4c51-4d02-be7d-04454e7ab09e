-- ETL建表语句
-- ads指标
drop table ads_iot_metric;
create table ads_iot_metric
(
    id             int auto_increment primary key,
    `device_num`   varchar(100) comment '设备标识符',
    `metric_key`   varchar(100) comment '指标名称',
    `metric_value` varchar(1000) comment '指标数据',
    update_time    varchar(20) comment '更新时间',
    remark          varchar(100) comment '备注',
    index device_num_index (device_num),
    index metric_key_index (metric_key)
) comment 'iot指标';

select *
from ads_iot_metric;


select id,
       value_s2 as S2,
       value_s1 as S1,
       date_format(update_time,'%Y-%m-%d %H:00:00') as date
from sugar_temperature
where date>DATE_SUB(CURDATE(),INTERVAL 7 day)
ORDER BY
    date ASC;

select sum(metric_value) as metric_value from  ads_iot_metric aim
where ( device_num='BTank' and metric_key='TankBUnit01Actual') or
    ( device_num='RefinedSugarR3' and metric_key='RefinedSugarR3DayTraffic')
;


