-- 新 查询
select '西安环球'                                                sale_company,
       t2.deparment_code                                         deparment_code,
       t2.deparment_name                                         deparment_name,
       t2.department_region                                      region,
       ifnull(t2.cust_name, t1.cust_name)                        cust_name,
       t2.cust_manager_code,
       t2.cust_manager_name,
       t2.cust_type,
       CASE
           WHEN t2.cust_type = 0 THEN '新建客户'
           WHEN t2.cust_type = 1 THEN '公海客户'
           WHEN t2.cust_type = 2 THEN '合作客户'
           WHEN t2.cust_type = 3 THEN '开发中客户'
           WHEN t2.cust_type = 4 THEN '受限客户'
           END                                                   cust_type_name,
       qkbbbye                                                   outstanding_amount,
       qkts                                                      outstanding_days,
       DATE_SUB(concat(:search_month, '-01'), INTERVAL 1 DAY) AS last_month_end
from crm_account_receivable_age t1
         left join (WITH RankedCustomers AS (SELECT *,
                                                    ROW_NUMBER() OVER (PARTITION BY cust_code ORDER BY update_time DESC) as rn
                                             FROM crm_cust_basic
                                             where flag_deleted = 0)
                    SELECT *
                    FROM RankedCustomers
                    WHERE rn = 1) t2 on t1.cust_code = t2.cust_code

where t1.flag_deleted = 0
  and t2.flag_deleted = 0
  and date_format(t1.create_time, '%Y-%m') = :search_month
and t1.cust_name='武汉生物制品研究所有限责任公司'
#   AND if(:admin,
#          1,
#          if(:cust_code_size>0, t2.cust_code in (:cust_code_arr), 1)
#       )
#   AND ((:deparment_code IS NULL OR :deparment_code = '') OR (t2.deparment_code = :deparment_code))
#   AND ((:region IS NULL OR :region = '') OR (t2.department_region = :region))
#   AND ((:cust_code IS NULL OR :cust_code = '') OR (t2.cust_code like concat('%', :cust_code, '%')))
#   AND ((:cust_name IS NULL OR :cust_name = '') OR (t2.cust_name LIKE concat('%', :cust_name, '%')))
#   AND if(:cust_manager_size>0, t2.cust_manager_code in (:cust_manager_arr), 1)
#   AND if(1>0, t2.cust_manager_code in ('1867047194728681476'), 1)
# LIMIT :page_size offset :offset
;
select DATE_SUB(concat(:search_month,'-01'), INTERVAL 1 DAY) AS        last_month_end;

-- 合计
SELECT '合计' AS               sale_company,
       round(sum(outstanding_amount),2) outstanding_amount
FROM (
         select '西安环球'        sale_company,
                t2.deparment_code deparment_code,
                t2.deparment_name deparment_name,
                t2.department_region         region,
                ifnull(t2.cust_name,t1.cust_name) cust_name,
                t2.cust_manager_code,
                t2.cust_manager_name,
                t2.cust_type,
                qkbbbye outstanding_amount,
                qkts outstanding_days
         from crm_account_receivable_age t1
                  left join (
             WITH RankedCustomers AS (
                 SELECT
                     *,
                     ROW_NUMBER() OVER (PARTITION BY cust_code ORDER BY update_time DESC) as rn
                 FROM crm_cust_basic
             )
             SELECT
                 *
             FROM RankedCustomers
             WHERE rn = 1
         ) t2 on t1.cust_code=t2.cust_code
         where
             t1.flag_deleted=0
           and t2.flag_deleted=0
           and date_format(t1.create_time,'%Y-%m-%d')=current_date
           AND if(:admin,
                  if(:condition, t2.cust_manager_code in (:cust_manager_code_arr), 1),
                  if(:condition,
                     t2.cust_manager_code in (:cust_manager_code_arr) and
                     t2.sales_assistant_code like concat('%', :user_id, '%'),
                     t2.cust_manager_code in (:cust_manager_code_arr) or t2.sales_assistant_code like concat('%', :user_id, '%'))
               )
           AND ((:region IS NULL OR :region = '') OR (t2.department_region = :region))
           AND ((:cust_code IS NULL OR :cust_code = '') OR (t2.cust_code like concat('%', :cust_code, '%')))
           AND ((:cust_name IS NULL OR :cust_name = '') OR (t2.cust_name LIKE concat('%', :cust_name, '%')))
           AND if(:cust_manager_size>0, t2.cust_manager_code in (:cust_manager_arr), 1)
     )temp;


select * from crm_sales_received_payments;
select '西安环球'        sale_company,
       t2.deparment_code deparment_code,
       t2.deparment_name deparment_name,
       t2.department_region         region,
       ifnull(t2.cust_name,t1.cust_name) cust_name,
       t2.cust_manager_code,
       t2.cust_manager_name,
       t2.cust_type,
       CASE
           WHEN t2.cust_type = 0 THEN '新建客户'
           WHEN t2.cust_type = 1 THEN '公海客户'
           WHEN t2.cust_type = 2 THEN '合作客户'
           WHEN t2.cust_type = 3 THEN '开发中客户'
           WHEN t2.cust_type = 4 THEN '受限客户'
           END                                 cust_type_name,
       qkbbbye outstanding_amount,
       qkts outstanding_days
from crm_account_receivable_age t1
         left join (
    WITH RankedCustomers AS (
        SELECT
            *,
            ROW_NUMBER() OVER (PARTITION BY cust_code ORDER BY update_time DESC) as rn
        FROM crm_cust_basic
    )
    SELECT
        *
    FROM RankedCustomers
    WHERE rn = 1
) t2 on t1.cust_code=t2.cust_code
where
    t1.flag_deleted=0
  and t2.flag_deleted=0
  and date_format(t1.create_time,'%Y-%m')=:search_month
  AND if(1,
         if(0, t2.cust_manager_code in (''), 1),
         if(0,
            t2.cust_manager_code in ('') and
            t2.sales_assistant_code like concat('%', '', '%'),
            t2.cust_manager_code in ('') or t2.sales_assistant_code like concat('%', '', '%'))
      )
  AND ((:region IS NULL OR :region = '') OR (t2.department_region = :region))
  AND ((:cust_code IS NULL OR :cust_code = '') OR (t2.cust_code like concat('%', :cust_code, '%')))
  AND ((:cust_name IS NULL OR :cust_name = '') OR (t2.cust_name LIKE concat('%', :cust_name, '%')))
  AND if(:cust_manager_size>0, t2.cust_manager_code in ('1867047194728681476'), 1)
LIMIT :page_size offset :offset;
