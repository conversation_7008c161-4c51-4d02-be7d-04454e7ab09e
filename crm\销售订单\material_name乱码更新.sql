select * from crm_contract_management_product ccmp where material_name
 REGEXP '[\\u200B-\\u200D\\uFEFF]|[\\x00-\\x1F]';
UPDATE crm_contract_management_product
SET material_name = REGEXP_REPLACE(material_name, '[\\u200B-\\u200D\\uFEFF]|[\\x00-\\x1F]', '')
WHERE material_name REGEXP '[\\u200B-\\u200D\\uFEFF]|[\\x00-\\x1F]';

select * from crm_sales_order_product csop  where material_name
REGEXP '[\\u200B-\\u200D\\uFEFF]|[\\x00-\\x1F]';
UPDATE crm_sales_order_product
SET material_name = REGEXP_REPLACE(material_name, '[\\u200B-\\u200D\\uFEFF]|[\\x00-\\x1F]', '')
WHERE material_name REGEXP '[\\u200B-\\u200D\\uFEFF]|[\\x00-\\x1F]';

select * from crm_quotation cq where material_name REGEXP '[\\u200B-\\u200D\\uFEFF]|[\\x00-\\x1F]';
UPDATE crm_quotation
SET material_name = REGEXP_REPLACE(material_name, '[\\u200B-\\u200D\\uFEFF]|[\\x00-\\x1F]', '')
WHERE material_name REGEXP '[\\u200B-\\u200D\\uFEFF]|[\\x00-\\x1F]';

select * from crm_preliminary_quotation_product cq where material_name REGEXP '[\\u200B-\\u200D\\uFEFF]|[\\x00-\\x1F]';
UPDATE crm_preliminary_quotation_product
SET material_name = REGEXP_REPLACE(material_name, '[\\u200B-\\u200D\\uFEFF]|[\\x00-\\x1F]', '')
WHERE material_name REGEXP '[\\u200B-\\u200D\\uFEFF]|[\\x00-\\x1F]';


