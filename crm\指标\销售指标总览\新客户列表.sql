-- 当年新客户开发列表
select t1.cust_code,
       t1.cust_name,
       round(sum(t1.invoiced_amount),2) sales_amount,
       round(sum(t1.received_money),2) return_amount,
       t1.cust_manager_name salesman,
       min(t1.development_date) development_date,
      t2.arrears_amount ,
      t2.arrears_age_days
from cockpit.dws_new_customer t1 left join
    (select cust_code,
            round(sum(ocar.qkbbbye), 2) arrears_amount,
            max(ocar.qkts)              arrears_age_days
     from cockpit.ods_crm_account_receivable_age ocar
     where date_format(ocar.create_time, '%Y-%m') = date_format(now(), '%Y-%m')
     group by cust_code) t2 on t1.cust_code=t2.cust_code
where t1.metric_year= year(now())
group by t1.cust_code,
         t1.cust_name,
       cust_manager_code,
         cust_manager_name
order by  sales_amount desc limit 10
;
-- 357490.68
select * from cockpit.dws_new_customer where cust_code='C004654';

select cust_code,
       round(sum(t2.qkbbbye), 2) arrears_amount,
       max(t2.qkts)              arrears_age_days
from cockpit.ods_crm_account_receivable_age t2
where date_format(t2.create_time, '%Y-%m') = date_format(now(), '%Y-%m')
group by cust_code
;


select * from dws_new_customer where cust_code='C000001';
select * from ods_crm_account_receivable_age where cust_code='C000001';
select * from ods_crm_account_receivable_age where date_format(create_time,'%Y-%m') = date_format(now(),'%Y-%m');
-- ETL:CRM应收账龄同步
create table ods_crm_account_receivable_age
(
    ywy          varchar(255)  null comment '业务员',
    qkts         varchar(255)  null comment '欠款天数',
    cust_code    varchar(255)  null comment '客户编码',
    qkbbbye      varchar(255)  null comment '本币余额',
    qkmx         varchar(255)  null comment '余额',
    cust_name    varchar(255)  null comment '客户名称',
    id           int auto_increment comment '自增主键'
        primary key,
    flag_deleted int default 0 null comment '是否删除',
    version      varchar(20)   null comment '版本',
    create_by    varchar(50)   null comment '创建人',
    create_time  datetime      null comment '创建时间',
    update_by    varchar(50)   null comment '修改人',
    update_time  datetime      null comment '修改时间',
    domain_id    varchar(50)   null comment '域id'
)
    comment '应收账龄';

