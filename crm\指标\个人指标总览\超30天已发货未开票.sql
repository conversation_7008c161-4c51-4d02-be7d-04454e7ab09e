select count(*) cust_size, date_format(update_time, '%Y-%m') update_time
from dws_shipped_uninvoiced_orders
where uninvoiced_age > 30
  and date_format(update_time, '%Y') in (year(now()), year(now()) - 1)
group by update_time;
;
-- 取未开票产品数量
select temp1.`year`, temp1.`month`, ifnull(temp2.cust_size, 0) cust_size
from (select `year_month`, `year`, `month`
      from cockpit.dim_month dm
      where dm.year in (year(now()), year(now()) - 1)) temp1
         left join (select count(*) cust_size, date_format(update_time, '%Y-%m') update_time
                    from dws_shipped_uninvoiced_orders
                    where uninvoiced_age > 30
                      AND if(:admin,
                             1,
                             if(:cust_code_size > 0, cust_code in (:cust_code_arr), 1)
                          )
                      and date_format(update_time, '%Y') in (year(now()), year(now()) - 1)
                    group by update_time) temp2 on temp1.`year_month` = temp2.update_time
order by temp1.`year_month`;

