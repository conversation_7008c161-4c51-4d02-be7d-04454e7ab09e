drop table dwd_customer_risk_data;
CREATE TABLE dwd_customer_risk_data
(
    id         INT AUTO_INCREMENT PRIMARY KEY,
    cust_code  VARCHAR(50) COMMENT '客户编码',
    cust_name  VARCHAR(100) COMMENT '客户名称',
    amount     varchar(100) DEFAULT 0 COMMENT '金额',
    days       varchar(100) DEFAULT 0 COMMENT '天数',
    risk_score varchar(100) DEFAULT 0 COMMENT '风险评分',
    risk_level VARCHAR(20) NOT NULL COMMENT '风险等级',
    data_year  varchar(4)  NOT NULL comment '数据年份',
    data_month varchar(2)  NOT NULL COMMENT '数据月份',
    type       varchar(2) comment '类型:1-库存2-欠款3-已发货未开票',
    INDEX idx_cust_code (cust_code),
    INDEX idx_data_year (data_year),
    INDEX idx_data_month (data_month)
) COMMENT ='客户风险数据表';
select *
from dwd_customer_risk_data
where type=:type
and concat(data_year,'-',data_month) = DATE_FORMAT(DATE_SUB(CURRENT_DATE(), INTERVAL 1 MONTH), '%Y-%m')
order by cast(risk_score as signed) desc limit 10
;

SELECT
       cust_code,
       cust_name,
       MAX(CASE
               WHEN concat(data_year, '-', data_month) =
                    DATE_FORMAT(DATE_SUB(CURRENT_DATE(), INTERVAL 1 MONTH), '%Y-%m')
                   THEN amount END)                                                    as 'last_month_amount',
       MAX(CASE
               WHEN concat(data_year, '-', data_month) =
                    DATE_FORMAT(DATE_SUB(CURRENT_DATE(), INTERVAL 1 MONTH), '%Y-%m')
                   THEN days END)                                                      as 'last_month_days',
       MAX(CASE WHEN data_year = :data_year and data_month = '01' THEN risk_score END) as 'score_01',
       MAX(CASE WHEN data_year = :data_year and data_month = '01' THEN risk_level END) as 'level_01',
       MAX(CASE WHEN data_year = :data_year and data_month = '02' THEN risk_score END) as 'score_02',
       MAX(CASE WHEN data_year = :data_year and data_month = '02' THEN risk_level END) as 'level_02',
       MAX(CASE WHEN data_year = :data_year and data_month = '03' THEN risk_score END) as 'score_03',
       MAX(CASE WHEN data_year = :data_year and data_month = '03' THEN risk_level END) as 'level_03',
       MAX(CASE WHEN data_year = :data_year and data_month = '04' THEN risk_score END) as 'score_04',
       MAX(CASE WHEN data_year = :data_year and data_month = '04' THEN risk_level END) as 'level_04',
       MAX(CASE WHEN data_year = :data_year and data_month = '05' THEN risk_score END) as 'score_05',
       MAX(CASE WHEN data_year = :data_year and data_month = '05' THEN risk_level END) as 'level_05',
       MAX(CASE WHEN data_year = :data_year and data_month = '06' THEN risk_score END) as 'score_06',
       MAX(CASE WHEN data_year = :data_year and data_month = '06' THEN risk_level END) as 'level_06',
       MAX(CASE WHEN data_year = :data_year and data_month = '07' THEN risk_score END) as 'score_07',
       MAX(CASE WHEN data_year = :data_year and data_month = '07' THEN risk_level END) as 'level_07',
       MAX(CASE WHEN data_year = :data_year and data_month = '08' THEN risk_score END) as 'score_08',
       MAX(CASE WHEN data_year = :data_year and data_month = '08' THEN risk_level END) as 'level_08',
       MAX(CASE WHEN data_year = :data_year and data_month = '09' THEN risk_score END) as 'score_09',
       MAX(CASE WHEN data_year = :data_year and data_month = '09' THEN risk_level END) as 'level_09',
       MAX(CASE WHEN data_year = :data_year and data_month = '10' THEN risk_score END) as 'score_10',
       MAX(CASE WHEN data_year = :data_year and data_month = '10' THEN risk_level END) as 'level_10',
       MAX(CASE WHEN data_year = :data_year and data_month = '11' THEN risk_score END) as 'score_11',
       MAX(CASE WHEN data_year = :data_year and data_month = '11' THEN risk_level END) as 'level_11',
       MAX(CASE WHEN data_year = :data_year and data_month = '12' THEN risk_score END) as 'score_12',
       MAX(CASE WHEN data_year = :data_year and data_month = '12' THEN risk_level END) as 'level_12'
FROM cockpit.dwd_customer_risk_data
WHERE type = :type
  AND if(:admin,
         1,
         if(:cust_code_size > 0, cust_code in (:cust_code_arr), 1)
      )
  AND ((:cust_code IS NULL OR :cust_code = '') OR (cust_code like concat('%', :cust_code, '%')))
  AND ((:cust_name IS NULL OR :cust_name = '') OR (cust_name LIKE concat('%', :cust_name, '%')))
  AND ((:risk_level IS NULL OR :risk_level = '') OR (risk_level = :risk_level))
GROUP BY cust_code, cust_name
order by MAX(CASE
                 WHEN data_month = DATE_FORMAT(DATE_SUB(CURRENT_DATE(), INTERVAL 1 MONTH), '%m')
                     THEN cast(risk_score as signed) END) desc
LIMIT :page_size offset :offset
;
select DATE_FORMAT(DATE_SUB(CURRENT_DATE(), INTERVAL 1 MONTH), '%Y-%m');
;


