-- 申请入库
-- 领用单号查询wms信息  pro_record_out.pick_code
select pdo.*,
       pi.id,pi.prd_code as pi_code,pi.batch,pi.inventory_quantity,pi.record_by,pi.create_time,pi.accessory_code,
       ppr.packing_type,ppr.ranks,ppr.product_code,ppr.product_name,ppr.sold_party sold_party_1,ppr.csr_name csr_name_1,ppr.number_plate,ppr.model model_1,
       de.model,de.id as deid,de.outer_pack_quantity,de.inkjet_code,de.sold_party,de.csr_name,de.pro_name,de.pro_code,de.packing_type packing_type_1,
       icr.inkjet_code inkjet_code_1
from pro_record_out_detail pdo
         left join pro_inventory pi on pdo.inventory_id = pi.id
         left join packaging_record ppr on ppr.packing_code = pi.prd_code
         left join inkjet_coding_records icr on ppr.inkjet_id = icr.inkjet_id
         left join packaging_record_details de on de.id = pi.detail_id
where pdo.pick_code = :pick_code
  and pdo.flag_deleted = 0;


select * from pro_inventory pi where pick_no='WRC202506300003';
select * from pro_inventory pi where accessory_code='N120200020149' ;
