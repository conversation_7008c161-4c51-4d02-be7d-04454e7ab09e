-- pdm_product_version
select * from pdm_product_version ppv where  material_name REGEXP '[\\u200B-\\u200D\\uFEFF]|[\\x00-\\x1F]';
UPDATE pdm_product_version
SET material_name = REGEXP_REPLACE(material_name, '[\\u200B-\\u200D\\uFEFF]|[\\x00-\\x1F]', '')
WHERE material_name REGEXP '[\\u200B-\\u200D\\uFEFF]|[\\x00-\\x1F]';

-- pdm_material
select * from pdm_material ppv where  material_name REGEXP '[\\u200B-\\u200D\\uFEFF]|[\\x00-\\x1F]';
UPDATE pdm_material
SET material_name = REGEXP_REPLACE(material_name, '[\\u200B-\\u200D\\uFEFF]|[\\x00-\\x1F]', '')
WHERE material_name REGEXP '[\\u200B-\\u200D\\uFEFF]|[\\x00-\\x1F]';

-- pdm_product_bom
select * from pdm_product_ban_assunit ppv where  edition_name REGEXP '[\\u200B-\\u200D\\uFEFF]|[\\x00-\\x1F]';
UPDATE pdm_product_ban_assunit
SET edition_name = REGEXP_REPLACE(edition_name, '[\\u200B-\\u200D\\uFEFF]|[\\x00-\\x1F]', '')
WHERE edition_name REGEXP '[\\u200B-\\u200D\\uFEFF]|[\\x00-\\x1F]';

-- pdm_product_bom
select * from pdm_edition ppv where  edition_name REGEXP '[\\u200B-\\u200D\\uFEFF]|[\\x00-\\x1F]';
UPDATE pdm_edition
SET edition_name = REGEXP_REPLACE(edition_name, '[\\u200B-\\u200D\\uFEFF]|[\\x00-\\x1F]', '')
WHERE edition_name REGEXP '[\\u200B-\\u200D\\uFEFF]|[\\x00-\\x1F]';

-- pdm_product_bom
select * from pdm_product_bom ppv where  material_name REGEXP '[\\u200B-\\u200D\\uFEFF]|[\\x00-\\x1F]';
UPDATE pdm_product_bom
SET material_name = REGEXP_REPLACE(material_name, '[\\u200B-\\u200D\\uFEFF]|[\\x00-\\x1F]', '')
WHERE material_name REGEXP '[\\u200B-\\u200D\\uFEFF]|[\\x00-\\x1F]';

-- pdm_product_client
select * from pdm_product_client ppv where  grade_name REGEXP '[\\u200B-\\u200D\\uFEFF]|[\\x00-\\x1F]';
UPDATE pdm_product_client
SET grade_name = REGEXP_REPLACE(grade_name, '[\\u200B-\\u200D\\uFEFF]|[\\x00-\\x1F]', '')
WHERE grade_name REGEXP '[\\u200B-\\u200D\\uFEFF]|[\\x00-\\x1F]';

-- pdm_product_craft
select * from pdm_product_craft ppv where  material_name REGEXP '[\\u200B-\\u200D\\uFEFF]|[\\x00-\\x1F]';
UPDATE pdm_product_craft
SET material_name = REGEXP_REPLACE(material_name, '[\\u200B-\\u200D\\uFEFF]|[\\x00-\\x1F]', '')
WHERE material_name REGEXP '[\\u200B-\\u200D\\uFEFF]|[\\x00-\\x1F]';
