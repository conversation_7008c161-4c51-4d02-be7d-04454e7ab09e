-- 当月打样订单数量 旧
SELECT COUNT(1) as order_num_total
FROM ods_pm_job_order
WHERE source_type = '0'
  AND flag_deleted = '0'
  AND production_order_status = '3'
  AND DATE_FORMAT(create_time, '%Y-%m') = DATE_FORMAT(CURDATE(), '%Y-%m');

-- 当月丹阳订单数量 新
SELECT COUNT(1) as order_num_total
FROM ods_pm_job_order
WHERE source_type = '0'
  AND flag_deleted = '0'
  AND DATE_FORMAT(create_time, '%Y-%m') = '2024-11';

-- 按月统计
-- 当月打样订单数量
SELECT COUNT(distinct id) as order_num_total, DATE_FORMAT(create_time, '%Y-%m') update_time
FROM ods_pm_job_order
WHERE source_type = '0'
  AND flag_deleted = '0'
group by DATE_FORMAT(create_time, '%Y-%m');

-- 打样工时
SELECT COALESCE(ROUND(SUM(opor.actual_time) / 60.0, 2), 0) AS total_actual_hours
FROM ods_pm_job_order opjo
         join
     ods_pm_oee_records opor
     on opjo.production_batch_number = opor.production_batch_no
where opjo.source_type = '0'
  AND opjo.flag_deleted = 0
  AND (opor.action = '生产' OR opor.action = '调试')
  AND DATE_FORMAT(opjo.create_time, '%Y-%m') = DATE_FORMAT(CURDATE(), '%Y-%m');
;

-- ETL脚本
select t1.update_month, total_actual_hours, order_num_total
from (SELECT DATE_FORMAT(opjo.create_time, '%Y-%m')                 update_month,
             COALESCE(ROUND(SUM(opor.actual_time) / 60.0, 2), 0) AS total_actual_hours
      FROM ods_pm_job_order opjo
               join ods_pm_oee_records opor on opjo.production_batch_number = opor.production_batch_no
      where opjo.source_type = '0'
        AND opjo.flag_deleted = 0
        AND (opor.action = '生产' OR opor.action = '调试')
      group by DATE_FORMAT(opjo.create_time, '%Y-%m')) t1
         left join
     (SELECT COUNT(distinct id) as order_num_total, DATE_FORMAT(create_time, '%Y-%m') update_month
      FROM ods_pm_job_order
      WHERE source_type = '0'
        AND flag_deleted = '0'
      group by DATE_FORMAT(create_time, '%Y-%m')) t2 on t1.update_month = t2.update_month;
-- ETL脚本2
select t1.`year_month`        update_month,
       COALESCE(work_time, 0) work_time,
       COALESCE(quantity, 0)  quantity
from dim_month t1
         left join dwd_sample_order_month t2
                   on t1.`year_month` = t2.update_month
order by update_month;

-- 建表
create table dwd_sample_order_month
(
    id           int(10) auto_increment primary key,
    update_month varchar(20)    null comment '更新月份',
    work_time    decimal(10, 2) null comment '工时',
    quantity     int            null comment '数量'
) comment '打样订单统计';

-- sql查询
SELECT '打样工时(h)'                                                                       kind,
       MAX(CASE WHEN right(update_month, 2) = '01' THEN round(work_time, 2) ELSE 0 END) AS 'mon_1',
       MAX(CASE WHEN right(update_month, 2) = '02' THEN round(work_time, 2) ELSE 0 END) AS 'mon_2',
       MAX(CASE WHEN right(update_month, 2) = '03' THEN round(work_time, 2) ELSE 0 END) AS 'mon_3',
       MAX(CASE WHEN right(update_month, 2) = '04' THEN round(work_time, 2) ELSE 0 END) AS 'mon_4',
       MAX(CASE WHEN right(update_month, 2) = '05' THEN round(work_time, 2) ELSE 0 END) AS 'mon_5',
       MAX(CASE WHEN right(update_month, 2) = '06' THEN round(work_time, 2) ELSE 0 END) AS 'mon_6',
       MAX(CASE WHEN right(update_month, 2) = '07' THEN round(work_time, 2) ELSE 0 END) AS 'mon_7',
       MAX(CASE WHEN right(update_month, 2) = '08' THEN round(work_time, 2) ELSE 0 END) AS 'mon_8',
       MAX(CASE WHEN right(update_month, 2) = '09' THEN round(work_time, 2) ELSE 0 END) AS 'mon_9',
       MAX(CASE WHEN right(update_month, 2) = '10' THEN round(work_time, 2) ELSE 0 END) AS 'mon_10',
       MAX(CASE WHEN right(update_month, 2) = '11' THEN round(work_time, 2) ELSE 0 END) AS 'mon_11',
       MAX(CASE WHEN right(update_month, 2) = '12' THEN round(work_time, 2) ELSE 0 END) AS 'mon_12'
FROM cockpit.dwd_sample_order_month
where ((:year IS NULL OR :year = '') OR (left(update_month, 4) = :year))
union all
SELECT '打样订单数量'                                                           kind,
       MAX(CASE WHEN right(update_month, 2) = '01' THEN quantity ELSE 0 END) AS 'mon_1',
       MAX(CASE WHEN right(update_month, 2) = '02' THEN quantity ELSE 0 END) AS 'mon_2',
       MAX(CASE WHEN right(update_month, 2) = '03' THEN quantity ELSE 0 END) AS 'mon_3',
       MAX(CASE WHEN right(update_month, 2) = '04' THEN quantity ELSE 0 END) AS 'mon_4',
       MAX(CASE WHEN right(update_month, 2) = '05' THEN quantity ELSE 0 END) AS 'mon_5',
       MAX(CASE WHEN right(update_month, 2) = '06' THEN quantity ELSE 0 END) AS 'mon_6',
       MAX(CASE WHEN right(update_month, 2) = '07' THEN quantity ELSE 0 END) AS 'mon_7',
       MAX(CASE WHEN right(update_month, 2) = '08' THEN quantity ELSE 0 END) AS 'mon_8',
       MAX(CASE WHEN right(update_month, 2) = '09' THEN quantity ELSE 0 END) AS 'mon_9',
       MAX(CASE WHEN right(update_month, 2) = '10' THEN quantity ELSE 0 END) AS 'mon_10',
       MAX(CASE WHEN right(update_month, 2) = '11' THEN quantity ELSE 0 END) AS 'mon_11',
       MAX(CASE WHEN right(update_month, 2) = '12' THEN quantity ELSE 0 END) AS 'mon_12'
FROM cockpit.dwd_sample_order_month
where ((:year IS NULL OR :year = '') OR (left(update_month, 4) = :year))
;

-- 折线图
select update_month, work_time, quantity
from cockpit.dwd_sample_order_month
where ((:year IS NULL OR :year = '') OR (left(update_month, 4) = :year))
order by update_month asc;

