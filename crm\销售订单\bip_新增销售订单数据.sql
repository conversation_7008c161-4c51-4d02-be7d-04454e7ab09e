SELECT
    IF(ISNULL(csop.mnemonic_code) OR csop.mnemonic_code = '',
       IF(ISNULL(csop.material_code) OR csop.material_code = '', '', csop.material_code),
       csop.mnemonic_code
    ) AS cmaterialvid,
    csop.material_code,
    csop.mnemonic_code,

    CONCAT(ROW_NUMBER() OVER (ORDER BY csop.id), '0') AS crowno,
    CAST(COALESCE(csop.order_quantity_after_split, '') AS CHAR) AS nqtunitnum,
    CAST(COALESCE(csop.id, '') AS CHAR) AS vbdef1,
    CAST(COALESCE(csop.delivery_date, '') AS CHAR) AS vbdef6,
    CAST(COALESCE(csop.quantity_fluctuation, '') AS CHAR) AS vbdef2,
    CAST(COALESCE(SUBSTRING_INDEX(csop.product_version, '.', 1), '') AS CHAR) AS vbdef3,
    '' AS vbdef14,
    CAST(COALESCE(ccb.cust_mnemonic_code, '') AS CHAR) AS vbdef15,
    CAST(COALESCE(cso.delivery_address, '') AS CHAR) AS vbdef16,
    CAST(COALESCE(cso.administrative_code, '') AS CHAR) AS vbdef17,
    CAST(COALESCE(cso.sales_order_code,'') AS CHAR) AS vbdef18,
    CAST(COALESCE(round(cpqp.purchase_price,2), '') AS CHAR) AS vbdef40,
    CAST(COALESCE(csop.tax_rate, '') AS CHAR) AS ctaxcodeid,
    'CN' AS crececountryid,
    CAST(COALESCE(csop.settlement_currency_price_exclusive, '') AS CHAR) AS norigprice,
    CAST(COALESCE(csop.settlement_currency_price_exclusive, '') AS CHAR) AS nqtorigprice,
    CAST(COALESCE(csop.settlement_currency_price_exclusive, '') AS CHAR) AS norignetprice,
    CAST(COALESCE(csop.settlement_currency_price_exclusive, '') AS CHAR) AS nqtorignetprice,
    CAST(COALESCE(csop.settlement_currency_price_inclusive, '') AS CHAR) AS norigtaxnetprice,
    CAST(COALESCE(csop.settlement_currency_price_inclusive, '') AS CHAR) AS nqtorigtaxnetprc,
    CAST(COALESCE(csop.settlement_currency_price_inclusive, '') AS CHAR) AS nqtorigtaxprice,
    CAST(COALESCE(csop.settlement_currency_price_inclusive, '') AS CHAR) AS norigtaxprice,
    CAST(COALESCE(csop.settlement_currency_amount_exclusive, '') AS CHAR) AS norigmny,
    CAST(COALESCE(csop.tax_rate * csop.settlement_currency_amount_inclusive, '') AS CHAR) AS ntax,
    CAST(COALESCE(csop.tax_rate, '') AS CHAR) AS ntaxrate,
    CAST(COALESCE(csop.settlement_currency_amount_inclusive, '') AS CHAR) AS norigtaxmny,
    CAST(COALESCE(csop.order_quantity_after_split, '') AS CHAR) AS nnum,
    CAST(COALESCE(csop.order_quantity_after_split, '') AS CHAR) AS nastnum,
    'N' AS blargessflag,
    CAST(COALESCE(csop.remark, '') AS CHAR) AS vrownote,
    CAST(COALESCE(cpqp.commission_print_number, '') AS CHAR) AS vbdef8,
    CAST(COALESCE(cpqp.object_id, '') AS CHAR) AS vbdef34,
    CAST(COALESCE(cpqp.grade_name, '') AS CHAR) AS vbdef35
FROM
    crm_sales_order cso
        LEFT JOIN crm_sales_order_product csop ON
        csop.sales_order_code = cso.sales_order_code
        LEFT JOIN crm_contract_management ccm ON
        ccm.contract_management_code = cso.contract_management_code
        LEFT JOIN crm_contract_management_product ccmp ON
        ccmp.contract_management_code = ccm.contract_management_code
            AND ccmp.material_code = csop.material_code
            AND ccmp.product_version = csop.product_version
            AND ccmp.contract_product_line_number = csop.contract_product_line_number
        LEFT JOIN crm_preliminary_quotation_product cpqp ON
        cpqp.preliminary_quotation_code = ccm.quotation_code
            AND cpqp.material_code = csop.material_code
            AND cpqp.product_version = csop.product_version
            AND cpqp.id = ccmp.quotation_product_id
            AND cpqp.flag_deleted = 0
        LEFT JOIN crm_cust_basic ccb ON
        ccb.cust_code = cso.cust_code
            AND ccb.flag_deleted = 0
            AND ccb.cust_version = '1'
            AND ccb.cust_type != '0'
	AND ccb.cust_status NOT IN ('0', '3')
WHERE
    cso.flag_deleted = 0
  AND csop.flag_deleted = 0
  AND cso.sales_order_code = '25030390A';

select t1.material_code,t1.mnemonic_code,
       t2.material_code,t2.mnemonic_code
         from crm_sales_order_product t1
    join h3chq_pdmbusiness1704287270935.pdm_product_version t2 on t1.material_code=t2.material_code
where t1.mnemonic_code!=t2.mnemonic_code
;

update  crm_sales_order_product t1
         join h3chq_pdmbusiness1704287270935.pdm_product_version t2 on t1.material_code=t2.material_code
set t1.mnemonic_code=t2.mnemonic_code
where t1.mnemonic_code!=t2.mnemonic_code;
