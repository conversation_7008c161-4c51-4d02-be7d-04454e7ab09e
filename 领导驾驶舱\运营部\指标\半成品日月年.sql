SELECT
    ROUND(AVG(time_difference), 1) AS value,
    DAY(subquery.min_time) AS time
FROM (
    SELECT
    DATEDIFF(ww.min_time,w.actual_start_time_operation ) AS time_difference,
    c.production_batch_number,ww.min_time
    FROM
    ods_pm_job_order c
    JOIN
    ods_pm_job_order_management d ON c.production_batch_number = d.production_batch_number
    JOIN
    ods_pm_job_detail i ON d.task_code = i.task_code
    JOIN
    ods_pm_final_batch_job_audit w ON i.id = w.job_strip_number
    JOIN
    (select pro_batch,min(create_time) as min_time from ods_pm_finsih_project_stock group by pro_batch)  ww ON c.production_batch_number = ww.pro_batch
    WHERE
    c.is_collage = 1 AND c.flag_deleted = 0 AND  c.large_category in ('6876','6901')  AND (i.before_process_code IS NULL OR i.before_process_code = '') AND i.special_type is null
    AND YEAR(ww.min_time) = YEAR(CURDATE())
    AND MONTH(ww.min_time) = MONTH(CURDATE())
    union ALL
    SELECT
    DATEDIFF(ww.min_time,w.actual_start_time_operation ) AS time_difference,
    c.production_batch_number,ww.min_time
    FROM
    ods_pm_job_order c
    LEFT JOIN ods_pm_job_order_middle f	ON c.production_batch_number = f.production_batch_number
    JOIN
    ods_pm_job_order_management d ON f.task_code = d.task_code
    JOIN
    ods_pm_job_detail i ON d.task_code = i.task_code
    JOIN
    ods_pm_final_batch_job_audit w ON i.id = w.job_strip_number
    JOIN
    (select pro_batch,min(create_time) as min_time from ods_pm_finsih_project_stock group by pro_batch)  ww ON c.production_batch_number = ww.pro_batch
    WHERE
    c.is_collage = 0 AND f.flag_deleted = 0 AND  c.large_category in ('6876','6901') AND c.flag_deleted = 0  AND (i.before_process_code IS NULL OR i.before_process_code = '') AND i.special_type is null
    AND YEAR(ww.min_time) = YEAR(CURDATE())
    AND MONTH(ww.min_time) = MONTH(CURDATE())
    ) AS subquery
GROUP BY
    time;


-- 月
SELECT
    ROUND(AVG(time_difference), 1) AS value,
    MONTH(subquery.min_time) AS time
FROM (
         SELECT
             DATEDIFF(ww.min_time,w.actual_start_time_operation ) AS time_difference,
             c.production_batch_number,ww.min_time
         FROM
             ods_pm_job_order c
                 JOIN
             ods_pm_job_order_management d ON c.production_batch_number = d.production_batch_number
                 JOIN
             ods_pm_job_detail i ON d.task_code = i.task_code
                 JOIN
             ods_pm_final_batch_job_audit w ON i.id = w.job_strip_number
                 JOIN
             (select pro_batch,min(create_time) as min_time from ods_pm_finsih_project_stock where YEAR(create_time) = YEAR(CURDATE())  group by pro_batch)  ww ON c.production_batch_number = ww.pro_batch
         WHERE
             c.is_collage = 1 AND  c.large_category in ('6876','6901') and  c.flag_deleted = 0 AND (i.before_process_code IS NULL OR i.before_process_code = '') AND i.special_type is null
           AND YEAR(ww.min_time) = YEAR(CURDATE())

         union ALL

         SELECT
             DATEDIFF(ww.min_time,w.actual_start_time_operation ) AS time_difference,
             c.production_batch_number,ww.min_time
         FROM
             ods_pm_job_order c

                 LEFT JOIN ods_pm_job_order_middle f	ON c.production_batch_number = f.production_batch_number
                 JOIN
             ods_pm_job_order_management d ON f.task_code = d.task_code
                 JOIN
             ods_pm_job_detail i ON d.task_code = i.task_code
                 JOIN
             ods_pm_final_batch_job_audit w ON i.id = w.job_strip_number
                 JOIN
             (select pro_batch,min(create_time) as min_time from ods_pm_finsih_project_stock where YEAR(create_time) = YEAR(CURDATE())   group by pro_batch)  ww ON c.production_batch_number = ww.pro_batch
         WHERE
             c.is_collage = 0 AND  c.large_category in ('6876','6901') AND f.flag_deleted = 0 AND c.flag_deleted = 0  AND (i.before_process_code IS NULL OR i.before_process_code = '') AND i.special_type is null
           AND YEAR(ww.min_time) = YEAR(CURDATE())
     ) AS subquery
GROUP BY
    time;

-- 年
SELECT
    ROUND(AVG(time_difference), 1) AS value,
    YEAR(subquery.min_time) AS time
FROM (
         SELECT
             DATEDIFF(ww.min_time,w.actual_start_time_operation ) AS time_difference,
             c.production_batch_number,ww.min_time
         FROM
             ods_pm_job_order c
                 JOIN
             ods_pm_job_order_management d ON c.production_batch_number = d.production_batch_number
                 JOIN
             ods_pm_job_detail i ON d.task_code = i.task_code
                 JOIN
             ods_pm_final_batch_job_audit w ON i.id = w.job_strip_number
                 JOIN
             (select pro_batch,min(create_time) as min_time from ods_pm_finsih_project_stock   group by pro_batch)  ww ON c.production_batch_number = ww.pro_batch
         WHERE
             c.is_collage = 1 AND  c.large_category in ('6876','6901') and  c.flag_deleted = 0 AND (i.before_process_code IS NULL OR i.before_process_code = '') AND i.special_type is null


         union ALL

         SELECT
             DATEDIFF(ww.min_time,w.actual_start_time_operation ) AS time_difference,
             c.production_batch_number,ww.min_time
         FROM
             ods_pm_job_order c

                 LEFT JOIN ods_pm_job_order_middle f	ON c.production_batch_number = f.production_batch_number
                 JOIN
             ods_pm_job_order_management d ON f.task_code = d.task_code
                 JOIN
             ods_pm_job_detail i ON d.task_code = i.task_code
                 JOIN
             ods_pm_final_batch_job_audit w ON i.id = w.job_strip_number
                 JOIN
             (select pro_batch,min(create_time) as min_time from ods_pm_finsih_project_stock    group by pro_batch)  ww ON c.production_batch_number = ww.pro_batch
         WHERE
             c.is_collage = 0 AND  c.large_category in ('6876','6901') AND f.flag_deleted = 0 AND c.flag_deleted = 0  AND (i.before_process_code IS NULL OR i.before_process_code = '') AND i.special_type is null

     ) AS subquery
GROUP BY
    time;
