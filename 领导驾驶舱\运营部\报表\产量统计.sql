-- ETL:运营部产量统计数据->dwd_production_value_month
SELECT
    md.date_only,
    md.product_big_category,
    md.product_big_category_name,
    COALESCE(dd.production_quantity, 0) production_quantity,
    COALESCE(dd.production_value, 0) production_value,
    0 production_value_yoy,
    0 production_quantity_yoy,
    0 production_value_mom,
    0 production_quantity_mom
FROM
    (
        SELECT
            `year_month` date_only,
            dpbc.code AS product_big_category,
            dpbc.name AS product_big_category_name
        FROM
            dim_month dm,
            dim_product_big_category dpbc
        WHERE
            dm.`year` = YEAR(CURDATE())
    ) md
LEFT JOIN
(
	SELECT
		DATE_FORMAT(opfps.update_time, '%Y-%m') AS date_only,
		dpbc.code AS product_big_category,
		dpbc.name AS product_big_category_name,
		COALESCE(ROUND(SUM(COALESCE(opfps.report_num, 0)) , 0), 0) AS production_quantity,
		COALESCE(ROUND(SUM(COALESCE(opfps.report_num, 0) * COALESCE(opop.unit_price_no, 0)) , 0), 0) AS production_value
	FROM
		dim_product_big_category dpbc
	LEFT JOIN ods_pm_job_order opjo ON
		opjo.large_category = dpbc.code
	LEFT JOIN ods_pm_finsih_project_stock opfps ON
		opfps.pro_batch = opjo.production_batch_number
	LEFT JOIN ods_pm_order_product opop ON
		opjo.order_code = opop.sales_number
		AND opjo.material_code = opop.product_number
	WHERE
		opfps.stock_type = '1'
		AND opfps.storage_status = '4'
		AND YEAR(opfps.update_time) = YEAR(CURDATE())
	GROUP BY
		opjo.large_category,
		dpbc.name,
		DATE_FORMAT(opfps.update_time, '%Y-%m')
) dd ON
    dd.date_only = md.date_only
    AND dd.product_big_category = md.product_big_category
ORDER BY
    md.date_only;
