CREATE TABLE `plan_clean_task_config` (
                                          `responsible_person` varchar(255) COMMENT '负责人员',
                                          `responsible_position` varchar(255) COMMENT '负责岗位',
                                          `section_code` varchar(255) COMMENT '工段代码',
                                          `section_name` varchar(255) COMMENT '所属工段',
                                          `cycle_type` varchar(255) COMMENT '首端循环,末端循环',
                                          `frequency_value` varchar(255) COMMENT '频率值：固定日期-每月具体日期，固定天数-间隔天数',
                                          `frequency_type` varchar(255) COMMENT '频率类型：1-固定日期，2-固定天数',
                                          `task_desc` varchar(1000) COMMENT '任务描述',
                                          `task_name` varchar(255) COMMENT '任务名称',
                                          `task_code` varchar(255) COMMENT '任务编号',
                                          `id` int NOT NULL  AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键',
                                          `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
                                          `version` varchar(20) COMMENT '版本',
                                          `create_by` varchar(50) COMMENT '创建人',
                                          `create_time` datetime COMMENT '创建时间',
                                          `update_by` varchar(50) COMMENT '修改人',
                                          `update_time` datetime COMMENT '修改时间'
) ENGINE=InnoDB COMMENT='清洁任务配置表';

CREATE TABLE `plan_clean_template_info` (
                                            `section_name` varchar(255) COMMENT '工段名称',
                                            `section_code` varchar(255) COMMENT '工段代码',
                                            `template_desc` varchar(255) COMMENT '模板描述',
                                            `template_name` varchar(255) COMMENT '模板名称',
                                            `template_code` varchar(255) COMMENT '模板编码',
                                            `id` int NOT NULL  AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键',
                                            `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
                                            `version` varchar(20) COMMENT '版本',
                                            `create_by` varchar(50) COMMENT '创建人',
                                            `create_time` datetime COMMENT '创建时间',
                                            `update_by` varchar(50) COMMENT '修改人',
                                            `update_time` datetime COMMENT '修改时间'
) ENGINE=InnoDB COMMENT='计划模板信息表';

CREATE TABLE `plan_clean_template_detail` (
                                              `task_code` varchar(255) COMMENT '新建字段1',
                                              `template_code` varchar(255) COMMENT '新建字段',
                                              `id` int NOT NULL  AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键',
                                              `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
                                              `version` varchar(20) COMMENT '版本',
                                              `create_by` varchar(50) COMMENT '创建人',
                                              `create_time` datetime COMMENT '创建时间',
                                              `update_by` varchar(50) COMMENT '修改人',
                                              `update_time` datetime COMMENT '修改时间'
) ENGINE=InnoDB COMMENT='计划模板明细表';

CREATE TABLE `plan_clean_distribution_info` (
                                                `status` varchar(255) COMMENT '状态',
                                                `distribution_desc` varchar(255) COMMENT '备注描述',
                                                `section_name` varchar(255) COMMENT '工段名称',
                                                `section_code` varchar(255) COMMENT '工段编码',
                                                `end_date` varchar(255) COMMENT '结束日期',
                                                `start_date` varchar(255) COMMENT '开始日期',
                                                `template_name` varchar(255) COMMENT '模板名称',
                                                `template_code` varchar(255) COMMENT '模板编码',
                                                `distribution_name` varchar(255) COMMENT '下发名称',
                                                `distribution_code` varchar(255) COMMENT '下发编码',
                                                `id` int NOT NULL  AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键',
                                                `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
                                                `version` varchar(20) COMMENT '版本',
                                                `create_by` varchar(50) COMMENT '创建人',
                                                `create_time` datetime COMMENT '创建时间',
                                                `update_by` varchar(50) COMMENT '修改人',
                                                `update_time` datetime COMMENT '修改时间'
) ENGINE=InnoDB COMMENT='清洁任务下发信息'
;

CREATE TABLE `plan_clean_record` (
                                     `content` varchar(255) COMMENT '操作内容',
                                     `id` int NOT NULL  AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键',
                                     `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
                                     `version` varchar(20) COMMENT '版本',
                                     `create_by` varchar(50) COMMENT '创建人',
                                     `create_time` datetime COMMENT '创建时间',
                                     `update_by` varchar(50) COMMENT '修改人',
                                     `update_time` datetime COMMENT '修改时间',
                                     `distribution_code` varchar(255) COMMENT '下发编码'
) ENGINE=InnoDB COMMENT='清洁计划操作记录'
;

CREATE TABLE `plan_clean_check_feedback` (
                                             `check_result` varchar(255) COMMENT '检查结果',
                                             `check_team` varchar(255) COMMENT '检查人员班组',
                                             `check_person` varchar(255) COMMENT '检查人员',
                                             `distribution_code` varchar(255) COMMENT '下发编码',
                                             `id` int NOT NULL  AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键',
                                             `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
                                             `version` varchar(20) COMMENT '版本',
                                             `create_by` varchar(50) COMMENT '创建人',
                                             `create_time` datetime COMMENT '创建时间',
                                             `update_by` varchar(50) COMMENT '修改人',
                                             `update_time` datetime COMMENT '修改时间'
) ENGINE=InnoDB COMMENT='清洁计划检查反馈'
;

CREATE TABLE `plan_clean_execution` (
                                        `id` int NOT NULL  AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键',
                                        `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
                                        `version` varchar(20) COMMENT '版本',
                                        `create_by` varchar(50) COMMENT '创建人',
                                        `create_time` datetime COMMENT '创建时间',
                                        `update_by` varchar(50) COMMENT '修改人',
                                        `update_time` datetime COMMENT '修改时间',
                                        `task_code` varchar(255) COMMENT '任务编号',
                                        `distribution_code` varchar(255) COMMENT '下发编码',
                                        `plan_json` text COMMENT '计划日期json',
                                        `frequency_value` varchar(255) COMMENT '频率值',
                                        `task_desc` varchar(255) COMMENT '任务描述',
                                        `responsible_person` varchar(255) COMMENT '负责人员',
                                        `responsible_position` varchar(255) COMMENT '负责岗位',
                                        `execution_json` text COMMENT '执行日期json'
) ENGINE=InnoDB COMMENT='清洁计划执行';


;




select t1.* from  plan_clean_task_config t1
join plan_clean_template_detail t2 on t1.task_code=t2.task_code
join plan_clean_template_info t3 on t3.template_code=t2.template_code
where t1.flag_deleted=0 and t2.flag_deleted=0 and t3.flag_deleted=0
and t3.template_code=:template_code
;

select distinct t1.section_code value,t1.section_name label,t1.*
from plan_clean_template_info t1
where t1.flag_deleted=0 ;
