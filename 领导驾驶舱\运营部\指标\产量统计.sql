SELECT ny.date_only,
       ny.product_big_category,
       ny.product_big_category_name,
       ny.production_value,
       ny.production_quantity,
       CASE
           WHEN COALESCE(ly.production_value, 0) = 0 THEN 0
           ELSE ROUND(100 * (ny.production_value - ly.production_value) / ly.production_value, 2)
           END production_value_yoy,
       CASE
           WHEN COALESCE(ly.production_quantity, 0) = 0 THEN 0
           ELSE ROUND(100 * (ny.production_quantity - ly.production_quantity) / ly.production_quantity, 2)
           END production_quantity_yoy,
       CASE
           WHEN COALESCE(ny2.production_value, 0) = 0 THEN 0
           ELSE ROUND(100 * (ny.production_value - ny2.production_value) / ny2.production_value, 2)
           END production_value_mom,
       CASE
           WHEN COALESCE(ny2.production_quantity, 0) = 0 THEN 0
           ELSE ROUND(100 * (ny.production_quantity - ny2.production_quantity) / ny2.production_quantity, 2)
           END production_quantity_mom
FROM (SELECT DATE_FORMAT(opfps.update_time, '%Y-%m')                                                     AS date_only,
             dpbc.code                                                                                   AS product_big_category,
             dpbc.name                                                                                   AS product_big_category_name,
             COALESCE(ROUND(SUM(COALESCE(opfps.report_num, 0)), 0), 0)                                   AS production_quantity,
             COALESCE(ROUND(SUM(COALESCE(opfps.report_num, 0) * COALESCE(opop.unit_price_no, 0)), 0),
                      0)                                                                                 AS production_value
      FROM dim_product_big_category dpbc
               LEFT JOIN ods_pm_job_order opjo ON
          opjo.large_category = dpbc.code
               LEFT JOIN ods_pm_finsih_project_stock opfps ON
          opfps.pro_batch = opjo.production_batch_number
               LEFT JOIN ods_pm_order_product opop ON
          opjo.order_code = opop.sales_number
              AND opjo.material_code = opop.product_number
      WHERE opfps.stock_type = '1'
        AND opfps.storage_status = '4'
        AND YEAR(opfps.update_time) = YEAR(CURDATE())
      GROUP BY opjo.large_category,
               dpbc.name,
               DATE_FORMAT(opfps.update_time, '%Y-%m')) ny
         LEFT JOIN
     (SELECT DATE_FORMAT(opfps.update_time, '%Y-%m')                                                     AS date_only,
             dpbc.code                                                                                   AS product_big_category,
             dpbc.name                                                                                   AS product_big_category_name,
             COALESCE(ROUND(SUM(COALESCE(opfps.report_num, 0)), 0), 0)                                   AS production_quantity,
             COALESCE(ROUND(SUM(COALESCE(opfps.report_num, 0) * COALESCE(opop.unit_price_no, 0)), 0),
                      0)                                                                                 AS production_value
      FROM dim_product_big_category dpbc
               LEFT JOIN ods_pm_job_order opjo ON
          opjo.large_category = dpbc.code
               LEFT JOIN ods_pm_finsih_project_stock opfps ON
          opfps.pro_batch = opjo.production_batch_number
               LEFT JOIN ods_pm_order_product opop ON
          opjo.order_code = opop.sales_number
              AND opjo.material_code = opop.product_number
      WHERE opfps.stock_type = '1'
        AND opfps.storage_status = '4'
        AND YEAR(opfps.update_time) = YEAR(CURDATE() - INTERVAL 1 YEAR)
      GROUP BY opjo.large_category,
               dpbc.name,
               DATE_FORMAT(opfps.update_time, '%Y-%m')) ly ON
         ly.date_only = ny.date_only
             AND ly.product_big_category = ny.product_big_category
         LEFT JOIN
     (SELECT DATE_FORMAT(opfps.update_time, '%Y-%m')                                                     AS date_only,
             dpbc.code                                                                                   AS product_big_category,
             dpbc.name                                                                                   AS product_big_category_name,
             COALESCE(ROUND(SUM(COALESCE(opfps.report_num, 0)), 0), 0)                                   AS production_quantity,
             COALESCE(ROUND(SUM(COALESCE(opfps.report_num, 0) * COALESCE(opop.unit_price_no, 0)), 0),
                      0)                                                                                 AS production_value
      FROM dim_product_big_category dpbc
               LEFT JOIN ods_pm_job_order opjo ON
          opjo.large_category = dpbc.code
               LEFT JOIN ods_pm_finsih_project_stock opfps ON
          opfps.pro_batch = opjo.production_batch_number
               LEFT JOIN ods_pm_order_product opop ON
          opjo.order_code = opop.sales_number
              AND opjo.material_code = opop.product_number
      WHERE opfps.stock_type = '1'
        AND opfps.storage_status = '4'
        AND YEAR(opfps.update_time) = YEAR(CURDATE())
      GROUP BY opjo.large_category,
               dpbc.name,
               DATE_FORMAT(opfps.update_time, '%Y-%m')) ny2 ON
         ny2.date_only =
         DATE_FORMAT(DATE_SUB(STR_TO_DATE(CONCAT(ny.date_only, '-01'), '%Y-%m-%d'), INTERVAL 1 MONTH), '%Y-%m')
             AND ny2.product_big_category = ny.product_big_category;


