-- 已发货未开票金额
SELECT t3.cust_code,
       t3.cust_name,
       round(sum((t4.shipped_quantity - t4.invoiced_quantity) * t2.unit_price_exclusive),
             2)                                      uninvoiced_amount,
       max(datediff(current_date, t4.shipment_date)) uninvoiced_age
FROM crm_sales_order t1
         LEFT JOIN crm_sales_order_product t2 ON t1.sales_order_code = t2.sales_order_code
         LEFT JOIN crm_cust_basic t3 ON t3.cust_code = t1.cust_code
         join (select split_order_no            sales_order_code,
                      product_code              material_code,
                      split_order_line_no,
                      group_concat(outbound_no) shipment_code,
                      max(outbound_date)        shipment_date,
                      sum(ship_quantity)        shipped_quantity,
                      sum(ifnull(nnum, 0))      invoiced_quantity
               from bip_outbound_order_detail bood
                        left join (select csrcid, csrcbid, sum(nnum) nnum
                                   from crm_sales_invoice_details
                                   where flag_deleted = 0
                                   group by csrcid, csrcbid) csid
                                  on bood.outbound_header = csid.csrcid and bood.outbound_line_id = csid.csrcbid
               where bood.flag_deleted = 0
               group by sales_order_code, material_code, split_order_line_no) t4
              on t4.sales_order_code = t2.sales_order_code and t4.material_code = t2.material_code
                  and t4.split_order_line_no = t2.contract_product_line_number
WHERE t1.flag_deleted = 0
  AND t2.flag_deleted = 0
  AND t3.flag_deleted = 0
  and t3.cust_status = 2
  and t3.cust_type not in (0)
  and t4.shipped_quantity > t4.invoiced_quantity
group by cust_code, cust_name
;


-- ETL:已发货未开票风险矩阵
WITH risk_base AS (SELECT t3.cust_code,
                          t3.cust_name,
                          round(sum((t4.shipped_quantity - t4.invoiced_quantity) * t2.unit_price_exclusive *
                                    t2.exchange_rate),
                                2)                                      as uninvoiced_amount,
                          max(datediff(current_date, t4.shipment_date)) as uninvoiced_age
                   FROM h3chq_crmbusiness1704287359505.crm_sales_order t1
                            LEFT JOIN h3chq_crmbusiness1704287359505.crm_sales_order_product t2
                                      ON t1.sales_order_code = t2.sales_order_code
                            LEFT JOIN h3chq_crmbusiness1704287359505.crm_cust_basic t3 ON t3.cust_code = t1.cust_code
                            join (select split_order_no            sales_order_code,
                                         product_code              material_code,
                                         split_order_line_no,
                                         group_concat(outbound_no) shipment_code,
                                         max(outbound_date)        shipment_date,
                                         sum(ship_quantity)        shipped_quantity,
                                         sum(ifnull(nnum, 0))      invoiced_quantity
                                  from h3chq_crmbusiness1704287359505.bip_outbound_order_detail bood
                                           left join (select csrcid, csrcbid, sum(nnum) nnum
                                                      from h3chq_crmbusiness1704287359505.crm_sales_invoice_details
                                                      where flag_deleted = 0
                                                      group by csrcid, csrcbid) csid
                                                     on bood.outbound_header = csid.csrcid and
                                                        bood.outbound_line_id = csid.csrcbid
                                  where bood.flag_deleted = 0
                                  group by sales_order_code, material_code, split_order_line_no) t4
                                 on t4.sales_order_code = t2.sales_order_code and t4.material_code = t2.material_code
                                     and t4.split_order_line_no = t2.contract_product_line_number
                   WHERE t1.flag_deleted = 0
                     AND t2.flag_deleted = 0
                     AND t3.flag_deleted = 0
                     and t3.cust_status = 2
                     and t3.cust_status=2 and t3.cust_type not in (0)
                     and t4.shipped_quantity > t4.invoiced_quantity
                     and t2.order_quantity_after_split>t4.invoiced_quantity
                   group by cust_code, cust_name),
     risk_score as (SELECT cust_code,
                           cust_name,
                           uninvoiced_amount,
                           uninvoiced_age,
                           CASE
                               -- >=10万档位
                               WHEN uninvoiced_amount >= 100000 and uninvoiced_age > 270 THEN 36
                               WHEN uninvoiced_amount >= 100000 and uninvoiced_age >= 181 THEN 30
                               WHEN uninvoiced_amount >= 100000 and uninvoiced_age >= 121 THEN 24
                               WHEN uninvoiced_amount >= 100000 and uninvoiced_age >= 91 THEN 18
                               WHEN uninvoiced_amount >= 100000 and uninvoiced_age >= 61 THEN 12
                               WHEN uninvoiced_amount >= 100000 and uninvoiced_age >= 31 THEN 6

                               -- 5-10万档位
                               WHEN uninvoiced_amount >= 50000 and uninvoiced_age > 270 THEN 30
                               WHEN uninvoiced_amount >= 50000 and uninvoiced_age >= 181 THEN 25
                               WHEN uninvoiced_amount >= 50000 and uninvoiced_age >= 121 THEN 20
                               WHEN uninvoiced_amount >= 50000 and uninvoiced_age >= 91 THEN 15
                               WHEN uninvoiced_amount >= 50000 and uninvoiced_age >= 61 THEN 10
                               WHEN uninvoiced_amount >= 50000 and uninvoiced_age >= 31 THEN 5

                               -- 3-5万档位
                               WHEN uninvoiced_amount >= 30000 and uninvoiced_age > 270 THEN 24
                               WHEN uninvoiced_amount >= 30000 and uninvoiced_age >= 181 THEN 20
                               WHEN uninvoiced_amount >= 30000 and uninvoiced_age >= 121 THEN 16
                               WHEN uninvoiced_amount >= 30000 and uninvoiced_age >= 91 THEN 12
                               WHEN uninvoiced_amount >= 30000 and uninvoiced_age >= 61 THEN 8
                               WHEN uninvoiced_amount >= 30000 and uninvoiced_age >= 31 THEN 4
                               ELSE 0
                               END as risk_score

                    FROM risk_base)
select cust_code,
       cust_name,
       uninvoiced_amount,
       uninvoiced_age,
       risk_score,
       case
           when risk_score >= 36 then '重大风险'
           when risk_score >= 24 then '高风险'
           when risk_score >= 13 then '中等风险'
           when risk_score >= 7 then '一般'
           when risk_score >= 3 then '低风险'
           else '无风险' end                                            risk_level,
       DATE_FORMAT(DATE_SUB(CURRENT_DATE(), INTERVAL 1 MONTH), '%Y') AS data_year,
       DATE_FORMAT(DATE_SUB(CURRENT_DATE(), INTERVAL 1 MONTH), '%m') AS data_month,
       3                                                                type
from risk_score
;

-- 评分


