-- 发货查询
WITH delivered_data AS (
    SELECT
        csp.sales_order_code,
        csi.material_code,
        SUM(csi.current_shipment_quantity) AS shipped_quantity
    FROM
        crm_shipping_info csi
            LEFT JOIN crm_shipment_plan csp ON
            csi.shipment_code = csp.shipment_code
    WHERE
        csi.flag_deleted = 0
      AND csp.flag_deleted = 0
      AND csp.shipment_status = '4'
      AND csp.sales_order_code = :sales_order_code
    GROUP BY
        csp.sales_order_code,
        csi.material_code
),
     notification_date AS (
         SELECT
             csi.contract_product_line_number,
             csp.sales_order_code,
             csi.material_code,
             SUM(csi.current_shipment_quantity) AS notified_shipment_quantity
         FROM
             crm_shipping_info csi
                 LEFT JOIN crm_shipment_plan csp ON
                 csi.shipment_code = csp.shipment_code
         WHERE
             csi.flag_deleted = 0
           AND csp.flag_deleted = 0
           AND csp.shipment_status != '4'
    AND csp.shipment_status != '3'
    AND csp.sales_order_code = :sales_order_code
GROUP BY
    csp.sales_order_code,
    csi.material_code,
    csi.contract_product_line_number
    )
SELECT
    csop.id,
    csop.material_code,
    csop.mnemonic_code,
    csop.material_name,
    csop.order_quantity_after_split,
    csop.contract_product_line_number,
    csop.total_order_quantity,
    csop.quantity_fluctuation,
    csop.delivered_quantity,
    IFNULL(csop.delivered_quantity, 0) AS shipped_quantity,
    IFNULL(nd.notified_shipment_quantity, 0) AS notified_shipment_quantity,
    csop.standard_unit
FROM
    crm_sales_order_product csop
        LEFT JOIN delivered_data dd ON
        dd.sales_order_code = csop.sales_order_code
            AND dd.material_code = csop.material_code
        LEFT JOIN notification_date nd ON
        nd.sales_order_code = csop.sales_order_code
            AND nd.material_code = csop.material_code
            and if(nd.contract_product_line_number is not null,nd.contract_product_line_number=csop.contract_product_line_number,1)
WHERE
    csop.flag_deleted = 0
  AND csop.sales_order_code = :sales_order_code;




SELECT
    contract_product_line_number,
    csp.sales_order_code,
    csi.material_code,
    SUM(csi.current_shipment_quantity) AS notified_shipment_quantity
FROM
    crm_shipping_info csi
        LEFT JOIN crm_shipment_plan csp ON
        csi.shipment_code = csp.shipment_code
WHERE
    csi.flag_deleted = 0
  AND csp.flag_deleted = 0
  AND csp.shipment_status != '4'
  AND csp.shipment_status != '3'
  AND csp.sales_order_code = :sales_order_code
GROUP BY
    csp.sales_order_code,
    csi.material_code,
    contract_product_line_number
    ;

select csi.* from crm_shipping_info csi
                 LEFT JOIN crm_shipment_plan csp ON
                 csi.shipment_code = csp.shipment_code
where sales_order_code='25030148A'
;

select count(*) from crm_shipping_info;

select t1.material_code,t1.contract_product_line_number,t1.order_quantity_after_split,
       t3.material_code,t3.contract_product_line_number,t3.order_quantity
from  crm_sales_order_product t1
left join crm_shipment_plan t2 on t1.sales_order_code=t2.sales_order_code
left join crm_shipping_info t3 on t2.shipment_code=t3.shipment_code and t1.material_code=t3.material_code
where t3.material_code is not null and t3.contract_product_line_number is null
;
-- 更新数据
update
 crm_sales_order_product t1
    left join crm_shipment_plan t2 on t1.sales_order_code=t2.sales_order_code
    left join crm_shipping_info t3 on t2.shipment_code=t3.shipment_code and t1.material_code=t3.material_code
set t3.contract_product_line_number=t1.contract_product_line_number
where t3.material_code is not null and t3.contract_product_line_number is null;

select * from crm_shipping_info where contract_product_line_number is null;

--
SELECT
    sales_order_code,
    material_code,

    COUNT(DISTINCT contract_product_line_number) as distinct_line_count
FROM
    crm_sales_order_product
where sales_order_code  in (select t2.sales_order_code from crm_shipment_plan t2
                                                             left join crm_shipping_info t3 on t2.shipment_code=t3.shipment_code
                                                      where t3.contract_product_line_number is null
                                                       )
and contract_product_line_number is not null
GROUP BY
    sales_order_code,
    material_code
HAVING
    COUNT(DISTINCT contract_product_line_number) > 1;


SELECT
    sales_order_code
    material_code
FROM
    crm_sales_order_product
where sales_order_code  in (select sales_order_code from crm_shipment_plan )
GROUP BY
    sales_order_code,
    material_code
HAVING
    COUNT(DISTINCT contract_product_line_number) > 1;

select material_code,contract_product_line_number,invoiced_quantity,delivered_quantity,order_quantity_after_split
from crm_sales_order_product where sales_order_code='25030141A';

select t2.sales_order_code,t3.* from crm_shipment_plan t2
                 left join crm_shipping_info t3 on t2.shipment_code=t3.shipment_code
where  t2.shipment_code='XSFH250401000032';

select sales_order_code,csop.* from crm_sales_order_product csop where material_code='WLQT0000116';


select * from  crm_sales_order_product csop where sales_order_code='25030597A' and material_code='ZDHY0001201';
select * from  crm_contract_management_product ccmp  where contract_management_code='25030597' and material_code='ZDHY0001201';
select * from crm_shipping_info csi where shipment_code='XSFH250401000032' and material_code='ZDHY0001201';


SELECT
    cso.csaleorderid,
    cso.factory_assigned,
    csp.shipment_code AS shipments_code,
    '1' AS shipments_type,
    csp.create_time AS production_date,
    csp.creator AS shipments_planner,
    '' AS shipments_department,
    csp.remark AS remark,
    '0' AS status_code,
    ccb.cust_code AS external_cust
FROM
    crm_sales_order cso
        LEFT JOIN crm_cust_basic ccb ON
        ccb.cust_code = cso.cust_code
            AND ccb.flag_deleted = 0
            AND ccb.cust_type not in ('0','1')
            AND ccb.cust_status =2
        LEFT JOIN crm_shipment_plan csp ON
        csp.sales_order_code = cso.sales_order_code
WHERE
    cso.flag_deleted = 0
  AND csp.flag_deleted = 0
  AND csp.shipment_code = :shipment_code;
SELECT
    csop.flag_deleted,
    csop.csaleorderbid,
    csp.shipment_code,
    cso.csaleorderid AS order_code,
    '1' AS row_no,
    csi.current_shipment_quantity AS main_quantity,
    csi.material_code AS product_number,
    csi.material_name AS product_name,
    csi.float_rate as rate,
    CAST(COALESCE(SUBSTRING_INDEX(csop.product_version, '.', 1), '') AS CHAR) AS product_version,
    cso.cust_name AS cust_name,
    cso.cust_code AS cust_code,
    '1' AS status,
    csp.arrival_date AS delivery_date,
    csp.shipment_date AS plan_delivery_date,
    csp.contact_phone AS receiving_phone,
    csp.contact_name AS consignee,
    csp.shipping_address AS delivery_address,
    csp.administrative_code AS region_code,
    '0' AS is_finished_product,
    csp.remark AS remark

FROM
    crm_sales_order cso
        LEFT JOIN crm_sales_order_product csop ON
        csop.sales_order_code = cso.sales_order_code
        LEFT JOIN crm_cust_basic ccb ON
        ccb.cust_code = cso.cust_code
            AND ccb.flag_deleted = 0
            AND ccb.cust_version = '1'
            AND ccb.cust_type != '0'
            AND ccb.cust_status NOT IN ('0', '3')
            AND (leader_code IS NULL OR leader_code = '')
        LEFT JOIN crm_shipment_plan csp ON
        csp.sales_order_code = cso.sales_order_code
        LEFT JOIN crm_shipping_info csi ON
        csi.shipment_code = csp.shipment_code
            AND csi.material_code = csop.material_code
WHERE
    cso.flag_deleted = 0
  AND csp.flag_deleted = 0
  AND csi.flag_deleted = 0
  and csop.flag_deleted = 0
  AND csp.shipment_code = :shipment_code;
select flag_deleted,csop.* from crm_sales_order_product csop where sales_order_code='25060321A';


SELECT
    pso.order_number,
    pjo.order_code
FROM
   hq_pm_b. pm_sale_order pso
        LEFT JOIN hq_pm_b.pm_job_order pjo ON
        pjo.order_code = pso.order_number
WHERE
    pso.flag_deleted = 0
  AND pjo.flag_deleted = 0
  AND pso.bip_main_no = :csaleorderid;
SELECT
    pso.order_number,
    pjo.order_code
FROM
    hq_pm_b. pm_sale_order pso
        LEFT JOIN hq_pm_b.pm_job_order pjo ON
        pjo.order_code = pso.order_number
WHERE
    pso.flag_deleted = 0
  AND pjo.flag_deleted = 0
  AND pso.order_number = 'L25060321A';
select * from hq_pm_b. pm_sale_order pso where pso.bip_main_no='1001A5100000012S6KEF';
