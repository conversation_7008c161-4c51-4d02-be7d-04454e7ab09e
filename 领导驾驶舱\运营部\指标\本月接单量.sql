SELECT COALESCE(ROUND(SUM(CASE
                              WHEN YEAR(a.create_time) = YEAR(CURDATE())
                                  AND MONTH(a.create_time) = MONTH(CURDATE())
                                  THEN COALESCE(a.release_quantity, 0) * COALESCE(b.unit_price_no, 0)
                              ELSE 0
    END) / 10000, 0), 0)                       AS yue_money,
       COALESCE(ROUND(SUM(CASE
                              WHEN YEAR(a.create_time) = YEAR(CURDATE())
                                  THEN COALESCE(a.release_quantity, 0) * COALESCE(b.unit_price_no, 0)
                              ELSE 0
           END) / 10000, 0), 0)                AS nian_money,
       DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s') AS time
FROM ods_pm_job_order a
         LEFT JOIN ods_pm_order_product b ON a.order_code = b.sales_number AND a.material_code = b.product_number
WHERE a.source_type = 1;

-- 新计算
SELECT
    COALESCE(
            ROUND(
                    SUM(
                            CASE

                                WHEN YEAR(a.create_time) = YEAR(CURDATE())
                                    AND MONTH(a.create_time) = MONTH(CURDATE()) THEN
                                    COALESCE(a.total, 0) * COALESCE(a.unit_price_no, 0) ELSE 0
                                END) / 10000,
                    0),
            0) AS yue_money,
    COALESCE(
            ROUND(
                    SUM(
                            CASE
                                WHEN YEAR(a.create_time) = YEAR(CURDATE()) THEN
                                    COALESCE(a.total, 0) * COALESCE(a.unit_price_no, 0) ELSE 0
                                END) / 10000,
                    0),
            0) AS nian_money,
    DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s') AS TIME
FROM
    dim_product_big_category dpbc
     left join ods_pm_order_product a ON a.major_categories = dpbc.code
        left JOIN ods_pm_sale_order b ON b.order_number = a.sales_number
WHERE
    b.status in(1,2,3,4);

-- 查询所有大类
select major_categories from
                  ods_pm_order_product a
        left JOIN ods_pm_sale_order b ON b.order_number = a.sales_number
WHERE
    b.status in(1,2,3,4) group by a.major_categories  ;
