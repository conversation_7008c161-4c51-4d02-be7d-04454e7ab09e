-- 班组合格率ETL 合格品收率=(成品量+抽样数量)/接收数量
select group_id,
       squad_name,
       date_format(create_time, '%Y-%m')                                            update_month,
       sum(feed_quantity)                                                           feed_quantity,       -- 投料量
       sum(deliverables)                                                            deliverables,        -- 成品量
       sum(machine_consumption)                                                     machine_consumption, -- 取样量
       sum(quantity_rejects)                                                        quantity_rejects,    -- 废品(机台)
       sum(quality_inspection)                                                      quality_inspection,  -- 废品(品检剔废)
       100 * (sum(deliverables) + sum(machine_consumption)) /
       sum(feed_quantity)  rate
from (select distinct group_id,
                      squad_name,
                      t1.create_time,
                      feed_quantity,
                      deliverables,
                      machine_consumption,
                      t1.quantity_rejects,
                      t2.quality_inspection
      from ods_pm_final_batch_job_audit t1
               left join ods_pm_team_production_details t2 on t2.batch_job_audit_id = t1.id
               left join ods_pm_team_maintenance t3 on t1.group_id = t3.id
      where t1.flag_deleted = 0
        and t2.flag_deleted = 0
        and t3.flag_deleted = 0) temp
group by update_month, group_id, squad_name
order by update_month, squad_name asc
;

drop table dwd_group_pass_rate_month;
CREATE TABLE dwd_group_pass_rate_month
(
    id                  int(10) auto_increment primary key not null comment '自增主键',
    group_id            INT                                NOT NULL comment '班组id',
    squad_name          VARCHAR(255)                       NOT NULL comment '班组名称',
    update_month        varchar(10)                        NOT NULL comment '更新月份',
    feed_quantity       int           DEFAULT 0 comment '投料量',         -- 投料量
    deliverables        int           DEFAULT 0 comment '成品量',         -- 成品量
    machine_consumption int           DEFAULT 0 comment '取样量',         -- 取样量
    quantity_rejects    int           DEFAULT 0 comment '废品(机台)',     -- 废品(机台)
    quality_inspection  int           DEFAULT 0 comment '废品(品检剔废)', -- 废品(品检剔废)
    rate                DECIMAL(5, 2) DEFAULT 0 comment '合格率'-- 合格率
) comment '班组合格率';

select squad_name,
       update_month,
       feed_quantity,
       deliverables,
       machine_consumption,
       quantity_rejects,
       quality_inspection,
       concat( rate, '%') rate
from cockpit.dwd_group_pass_rate_month
where ((:year IS NULL OR :year = '') OR (left(update_month, 4) = :year))
  and ((:group_id IS NULL OR :group_id = '') OR (group_id = :group_id))
order by update_month asc, squad_name asc
limit :page_size offset :offset
;

-- 班组下拉框
select distinct group_id value, squad_name label
from cockpit.dwd_group_pass_rate_month
order by squad_name asc
;
