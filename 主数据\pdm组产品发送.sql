select * from pdm_product_version ppv where material_code='ZDQX0001545';
-- 组产品同步
-- bom
select categroy,product_code,material_code,product_version,mine_version,ppb.* from pdm_product_bom ppb
        where parent_code='ZDZY0000886'  and flag_deleted = 0;


-- 版
select * from pdm_product_ban_assunit
         where product_code = 'ZDWL0000014' and product_version = '1.4' and flag_deleted = 0;

select * from pdm_product_craft
where product_code = 'ZDYY0002177' and product_version = '1.6' and flag_deleted = 0;

select * from pdm_product_craft where product_code='SMQX0000097';
-- where product_code = 'ZDHZ0000601' and product_version = '1.4' and flag_deleted = 0;


select * from pdm_resource_work;

-- 查询子产品

SELECT *
FROM pdm_product_version t1
         JOIN (
    SELECT
        material_code,
        MAX(CAST(SUBSTRING_INDEX(product_version, '.', 1) AS UNSIGNED) * 1000 +
            CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(product_version, '.', -1), '.', 1) AS UNSIGNED)) AS max_version
    FROM pdm_product_version
    where status in (1,5) and flag_deleted = 0 and
        material_code = :children_product_code AND
        LEFT(product_version, POSITION('.' IN product_version) - 1) = :children_product_version
) t2
              ON t1.material_code = t2.material_code
                  AND (CAST(SUBSTRING_INDEX(t1.product_version, '.', 1) AS UNSIGNED) * 1000 +
                       CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(t1.product_version, '.', -1), '.', 1) AS UNSIGNED)) = t2.max_version
where status in (1,5)  and flag_deleted = 0   limit 1;

select * from pdm_edition where edition_categroy is null;
select * from pdm_material pm where material_code='WWQT0000013';
