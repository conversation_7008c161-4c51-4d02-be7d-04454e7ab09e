WITH version_split AS (
    SELECT
        material_code,
        product_version,
        CAST(SUBSTRING_INDEX(product_version, '.', 1) AS UNSIGNED) AS major_version,
        CAST(SUBSTRING_INDEX(product_version, '.', -1) AS UNSIGNED) AS minor_version,
        status
    FROM pdm_product_version
    WHERE status IN (1, 5, 6)  -- 生效/锁定/归档状态
),
     latest_versions AS (
         SELECT
             material_code,
             major_version,
             MAX(minor_version) AS latest_minor_version
         FROM version_split
         GROUP BY material_code, major_version
     )
SELECT
    t1.material_code AS '产品编码',
    t1.mnemonic_code AS '助记码',
    -- t1.product_version AS '版本',
    v.major_version '大版本',
    t2.client_code AS '客户编码',
    t2.client_name AS '客户名称',
    t2.grade_name AS '客户品名',
    t2.object_id AS '客户料号',
    IF(t2.is_used = 0, '禁用', '激活') AS '客户关系状态'
FROM pdm_product_bom t1
         JOIN version_split v ON t1.product_code = v.material_code
    AND t1.product_version = v.product_version
         JOIN latest_versions lv ON v.material_code = lv.material_code
    AND v.major_version = lv.major_version
    AND v.minor_version = lv.latest_minor_version
         JOIN pdm_product_client t2 ON t1.product_code = t2.product_code
    AND t1.product_version = t2.product_version
WHERE t1.categroy = 3
ORDER BY t1.material_code, t1.product_version;
;
